/**
 * 情绪状态管理类
 * 负责管理角色的八维情绪状态和相关逻辑
 */
class EmotionState {
    constructor() {
        // 八维情绪模型
        this.emotions = {
            // 基础情绪
            happiness: 0.7,      // 快乐度 0-1
            energy: 0.8,         // 精力 0-1
            alertness: 0.6,      // 警觉性 0-1
            friendliness: 0.9,   // 友好度 0-1
            
            // 扩展情绪
            anger: 0.1,          // 愤怒 0-1
            fear: 0.2,           // 恐惧 0-1
            snark: 0.3,          // 贱气 0-1
            playfulness: 0.8     // 玩心 0-1
        };

        // 情绪系统配置
        this.config = {
            decayRate: 0.005, // 基础衰减速度

            // 特殊衰减配置（某些情绪衰减更快）
            specialDecayRates: {
                energy: 0.02,      // 精力快速衰减（4倍速度）
                alertness: 0.015   // 警觉快速衰减（3倍速度）
            },

            targetValues: {
                // 基础情绪默认值
                happiness: 0.7, 
                energy: 0.8, 
                alertness: 0.6, 
                friendliness: 0.9,
                
                // 扩展情绪默认值
                anger: 0.1,
                fear: 0.2,
                snark: 0.3,
                playfulness: 0.8
            },
            impactStrength: 0.4, // 大幅增加影响强度，让行为显著影响情绪
            
            // 情绪相互影响规则（大幅增强影响力）
            emotionInteractions: {
                // 愤怒会大幅降低友好度和快乐度
                anger: { friendliness: -0.8, happiness: -0.6, playfulness: -0.4 },
                // 恐惧会大幅降低精力和玩心
                fear: { energy: -0.7, playfulness: -0.9, happiness: -0.5 },
                // 贱气会降低友好度但增加玩心
                snark: { friendliness: -0.6, playfulness: 0.4, anger: 0.2 },
                // 玩心会大幅增加快乐度和精力
                playfulness: { happiness: 0.7, energy: 0.5, fear: -0.3 },
                // 快乐会增加其他正面情绪
                happiness: { friendliness: 0.3, playfulness: 0.4, anger: -0.2 },
                // 精力会增加警觉和玩心
                energy: { alertness: 0.3, playfulness: 0.2, fear: -0.2 }
            }
        };

        // 精力系统配置
        this.staminaSystem = {
            maxStamina: 100,
            currentStamina: 100,
            recoveryRate: 5,        // 每秒恢复5点精力
            recoveryDelay: 2000,    // 2秒后开始恢复
            lastActionTime: 0,

            // 动作精力消耗配置
            actionCosts: {
                // 基础动作
                'stand': 0,
                'jump': 15,

                // 战斗动作
                'kick': 20,
                'block': 10,
                'hurt': 0,      // 受伤不消耗精力

                // 表演动作
                'talking': 5,
                'dance': 25,
                'taunt': 15,
                'stretch': 10,

                // 默认消耗
                'default': 10
            }
        };

        // 日志控制
        this.lastRecoveryLogTime = 0;
        this.lastStaminaLogTime = 0;

        console.log('🎭 EmotionState 初始化完成（包含精力系统）');
    }

    /**
     * 设置特定情绪值
     * @param {string} emotion - 情绪类型
     * @param {number} value - 情绪值 (0-1)
     */
    setEmotion(emotion, value) {
        if (this.emotions.hasOwnProperty(emotion)) {
            this.emotions[emotion] = Math.max(0, Math.min(1, value));
            console.log(`😊 ${emotion} 设置为: ${this.emotions[emotion].toFixed(2)}`);
            return true;
        }
        console.log(`❌ 未知的情绪类型: ${emotion}`);
        return false;
    }

    /**
     * 获取所有情绪状态
     * @returns {Object} 情绪状态对象
     */
    getEmotions() {
        return { ...this.emotions };
    }

    /**
     * 获取主导情绪
     * @returns {string} 主导情绪名称
     */
    getDominantEmotion() {
        let maxValue = 0;
        let dominantEmotion = 'happiness';

        for (const [emotion, value] of Object.entries(this.emotions)) {
            if (value > maxValue) {
                maxValue = value;
                dominantEmotion = emotion;
            }
        }

        return dominantEmotion;
    }

    /**
     * 根据事件更新情绪状态
     * @param {string} eventType - 事件类型
     * @param {Object} eventData - 事件数据
     */
    updateFromEvent(eventType, eventData = {}) {
        const impact = this.config.impactStrength;

        switch (eventType) {
            case 'social':
                this.emotions.friendliness = Math.min(1, this.emotions.friendliness + impact * 1.5);
                this.emotions.happiness = Math.min(1, this.emotions.happiness + impact * 1.2);
                this.emotions.anger = Math.max(0, this.emotions.anger - impact * 0.8);
                this.emotions.snark = Math.max(0, this.emotions.snark - impact * 0.6);
                break;

            case 'performance':
                this.emotions.happiness = Math.min(1, this.emotions.happiness + impact * 1.5);
                this.emotions.energy = Math.max(0, this.emotions.energy - impact * 0.8);
                this.emotions.playfulness = Math.min(1, this.emotions.playfulness + impact * 1.2);
                this.emotions.fear = Math.max(0, this.emotions.fear - impact * 0.5);
                break;

            case 'active':
                this.emotions.energy = Math.min(1, this.emotions.energy + impact * 1.5);
                this.emotions.alertness = Math.min(1, this.emotions.alertness + impact * 1.0);
                this.emotions.playfulness = Math.min(1, this.emotions.playfulness + impact * 0.8);
                this.emotions.fear = Math.max(0, this.emotions.fear - impact * 0.6);
                break;
                
            case 'defensive':
                this.emotions.alertness = Math.min(1, this.emotions.alertness + impact * 1.5);
                this.emotions.fear = Math.min(1, this.emotions.fear - impact * 0.8);
                this.emotions.anger = Math.min(1, this.emotions.anger + impact * 0.8);
                this.emotions.happiness = Math.max(0, this.emotions.happiness - impact * 0.3);
                this.emotions.playfulness = Math.max(0, this.emotions.playfulness - impact * 0.5);
                // 新增：格挡成功增加贱气（"哼，就这点本事？"）
                this.emotions.snark = Math.min(1, this.emotions.snark + impact * 0.6);
                break;

            case 'aggressive':
               
                this.emotions.energy = Math.min(1, this.emotions.energy + impact * 1.2);
                this.emotions.friendliness = Math.max(0, this.emotions.friendliness - impact * 0.6);
                this.emotions.fear = Math.max(0, this.emotions.fear - impact * 0.4);
                // 修改：攻击行为释放愤怒，减少而不是增加
                this.emotions.anger = Math.max(0, this.emotions.anger - impact * 0.8);
                // 新增：攻击后获得满足感
                this.emotions.happiness = Math.min(1, this.emotions.happiness + impact * 0.4);
                break;
                
            case 'playful':
                this.emotions.playfulness = Math.min(1, this.emotions.playfulness + impact * 1.5);
                this.emotions.happiness = Math.min(1, this.emotions.happiness + impact * 1.2);
                this.emotions.energy = Math.min(1, this.emotions.energy + impact * 1.0);
                this.emotions.anger = Math.max(0, this.emotions.anger - impact * 1.0);
                this.emotions.fear = Math.max(0, this.emotions.fear - impact * 0.8);
                break;

            case 'snarky':
                this.emotions.snark = Math.min(1, this.emotions.snark + impact * 1.5);
                this.emotions.playfulness = Math.min(1, this.emotions.playfulness + impact * 0.8);
                this.emotions.friendliness = Math.max(0, this.emotions.friendliness - impact * 0.8);
                this.emotions.anger = Math.min(1, this.emotions.anger - impact * 0.5);
                break;

            case 'fearful':
                this.emotions.fear = Math.min(1, this.emotions.fear + impact * 1.8);
                this.emotions.alertness = Math.min(1, this.emotions.alertness + impact * 1.2);
                this.emotions.energy = Math.max(0, this.emotions.energy - impact * 1.0);
                this.emotions.playfulness = Math.max(0, this.emotions.playfulness - impact * 1.2);
                this.emotions.happiness = Math.max(0, this.emotions.happiness - impact * 0.8);
                break;

            case 'damage':
                const damage = eventData.damage || 1;
                this.emotions.happiness = Math.max(0, this.emotions.happiness - 0.3 * damage);
                this.emotions.alertness = Math.min(1, this.emotions.alertness + 0.4 * damage);
                this.emotions.anger = Math.min(1, this.emotions.anger + 0.35 * damage);
                this.emotions.fear = Math.min(1, this.emotions.fear + 0.25 * damage);
                this.emotions.playfulness = Math.max(0, this.emotions.playfulness - 0.4 * damage);
                this.emotions.friendliness = Math.max(0, this.emotions.friendliness - 0.15 * damage);
                this.emotions.snark = Math.max(0, this.emotions.snark - 0.3 * damage);
                break;

            case 'heal':
                const heal = eventData.heal || 1;
                this.emotions.happiness = Math.min(1, this.emotions.happiness + 0.25 * heal);
                this.emotions.anger = Math.max(0, this.emotions.anger - 0.2 * heal);
                this.emotions.fear = Math.max(0, this.emotions.fear - 0.2 * heal);
                this.emotions.playfulness = Math.min(1, this.emotions.playfulness + 0.15 * heal);
                this.emotions.energy = Math.min(1, this.emotions.energy + 0.1 * heal);
                break;

            case 'down':
                this.emotions.energy = Math.max(0, this.emotions.energy - 0.7);
                this.emotions.happiness = Math.max(0, this.emotions.happiness - 0.6);
                this.emotions.fear = Math.min(1, this.emotions.fear + 0.8);
                this.emotions.anger = Math.min(1, this.emotions.anger + 0.5);
                this.emotions.playfulness = Math.max(0, this.emotions.playfulness - 0.9);
                this.emotions.friendliness = Math.max(0, this.emotions.friendliness - 0.3);
                break;

            case 'revive':
                this.emotions.energy = Math.min(1, this.emotions.energy + 0.6);
                this.emotions.happiness = Math.min(1, this.emotions.happiness + 0.5);
                this.emotions.fear = Math.max(0, this.emotions.fear - 0.7);
                this.emotions.playfulness = Math.min(1, this.emotions.playfulness + 0.6);
                this.emotions.anger = Math.max(0, this.emotions.anger - 0.4);
                break;
        }

        // 应用情绪相互影响
        this.applyInteractions();

        // 只在重要事件时输出日志，避免频繁的heal事件日志
        if (eventType !== 'heal' && eventType !== 'damage') {
            console.log(`🎭 情绪状态更新 (${eventType}):`, this.getEmotions());
        }
    }

    /**
     * 应用情绪相互影响
     */
    applyInteractions() {
        const { emotionInteractions } = this.config;
        const tempChanges = {};

        // 计算所有情绪相互影响
        for (const [sourceEmotion, interactions] of Object.entries(emotionInteractions)) {
            const sourceValue = this.emotions[sourceEmotion];
            
            for (const [targetEmotion, multiplier] of Object.entries(interactions)) {
                if (!tempChanges[targetEmotion]) {
                    tempChanges[targetEmotion] = 0;
                }
                // 影响强度 = 源情绪值 × 影响系数 × 基础影响强度
                tempChanges[targetEmotion] += sourceValue * multiplier * this.config.impactStrength * 0.1;
            }
        }

        // 应用所有变化
        for (const [emotion, change] of Object.entries(tempChanges)) {
            if (this.emotions.hasOwnProperty(emotion)) {
                this.emotions[emotion] = Math.max(0, Math.min(1, this.emotions[emotion] + change));
            }
        }
    }

    /**
     * 应用情绪自然衰减（仅在倒下状态时调用）
     */
    applyDecay() {
        const { decayRate, targetValues, specialDecayRates } = this.config;

        for (const [emotion, target] of Object.entries(targetValues)) {
            const current = this.emotions[emotion];

            // 使用特殊衰减速度（如果配置了的话）
            const currentDecayRate = specialDecayRates[emotion] || decayRate;

            if (current > target) {
                this.emotions[emotion] = Math.max(target, current - currentDecayRate);
            } else if (current < target) {
                this.emotions[emotion] = Math.min(target, current + currentDecayRate);
            }
        }
    }

    /**
     * 重置所有情绪为默认值
     */
    reset() {
        this.emotions = { ...this.config.targetValues };
        console.log('🔄 情绪状态已重置为默认值');
    }

    /**
     * 应用特殊情绪衰减（正常状态下精力和警觉快速衰减）
     */
    applySpecialDecay() {
        const { specialDecayRates, targetValues } = this.config;

        // 只对配置了特殊衰减的情绪进行快速衰减
        for (const [emotion, decayRate] of Object.entries(specialDecayRates)) {
            const current = this.emotions[emotion];
            const target = targetValues[emotion];

            if (current > target) {
                this.emotions[emotion] = Math.max(target, current - decayRate);
            } else if (current < target) {
                this.emotions[emotion] = Math.min(target, current + decayRate);
            }
        }
    }

    /**
     * 倒下状态的情绪恢复（缓慢恢复到默认值）
     */
    applyDownStateRecovery() {
        // 在倒下状态时，情绪会缓慢恢复到默认值
        this.applyDecay();

        // 减少日志频率，避免刷屏
        if (!this.lastRecoveryLogTime || Date.now() - this.lastRecoveryLogTime > 2000) {
            console.log('😵 倒下状态中，情绪缓慢恢复...');
            this.lastRecoveryLogTime = Date.now();
        }
    }

    /**
     * 检查是否有足够精力执行动作
     * @param {string} actionName - 动作名称
     * @returns {boolean} 是否有足够精力
     */
    hasEnoughStamina(actionName) {
        const cost = this.getActionStaminaCost(actionName);
        return this.staminaSystem.currentStamina >= cost;
    }

    /**
     * 获取动作的精力消耗
     * @param {string} actionName - 动作名称
     * @returns {number} 精力消耗值
     */
    getActionStaminaCost(actionName) {
        return this.staminaSystem.actionCosts[actionName] || this.staminaSystem.actionCosts.default;
    }

    /**
     * 消耗精力执行动作
     * @param {string} actionName - 动作名称
     * @returns {boolean} 是否成功执行
     */
    consumeStamina(actionName) {
        const cost = this.getActionStaminaCost(actionName);

        if (this.staminaSystem.currentStamina >= cost) {
            this.staminaSystem.currentStamina = Math.max(0, this.staminaSystem.currentStamina - cost);
            this.staminaSystem.lastActionTime = Date.now();

            if (cost > 0) {
                console.log(`🔋 精力消耗: ${actionName} (-${cost}) 剩余: ${this.staminaSystem.currentStamina}/${this.staminaSystem.maxStamina}`);
            }

            return true;
        } else {
            console.log(`❌ 精力不足: ${actionName} 需要${cost}点，当前${this.staminaSystem.currentStamina}点`);
            return false;
        }
    }

    /**
     * 更新精力恢复
     * @param {number} deltaTime - 时间间隔（毫秒）
     */
    updateStaminaRecovery(deltaTime) {
        const now = Date.now();

        // 检查是否可以开始恢复精力
        if (this.staminaSystem.currentStamina < this.staminaSystem.maxStamina &&
            now - this.staminaSystem.lastActionTime > this.staminaSystem.recoveryDelay) {

            const recoveryAmount = this.staminaSystem.recoveryRate * (deltaTime / 1000);
            const oldStamina = this.staminaSystem.currentStamina;

            this.staminaSystem.currentStamina = Math.min(
                this.staminaSystem.maxStamina,
                this.staminaSystem.currentStamina + recoveryAmount
            );

            // 减少日志频率
            if (!this.lastStaminaLogTime || now - this.lastStaminaLogTime > 2000) {
                console.log(`🔋 精力恢复: ${oldStamina.toFixed(1)} → ${this.staminaSystem.currentStamina.toFixed(1)}/${this.staminaSystem.maxStamina}`);
                this.lastStaminaLogTime = now;
            }
        }
    }

    /**
     * 获取精力状态
     * @returns {Object} 精力状态信息
     */
    getStaminaStatus() {
        return {
            current: this.staminaSystem.currentStamina,
            max: this.staminaSystem.maxStamina,
            percentage: (this.staminaSystem.currentStamina / this.staminaSystem.maxStamina) * 100,
            isRecovering: Date.now() - this.staminaSystem.lastActionTime > this.staminaSystem.recoveryDelay
        };
    }

    /**
     * 重置精力到满值
     */
    resetStamina() {
        this.staminaSystem.currentStamina = this.staminaSystem.maxStamina;
        console.log('🔋 精力已重置为满值');
    }

    /**
     * 获取情绪状态的详细信息
     * @returns {Object} 详细的情绪状态信息
     */
    getDetailedState() {
        return {
            emotions: this.getEmotions(),
            dominantEmotion: this.getDominantEmotion(),
            config: { ...this.config }
        };
    }
}

export default EmotionState;
