/**
 * 动画控制器
 * 负责管理功夫狗的所有动画播放，包括动作切换和混合
 * 符合规则文件要求：基于AnimationMixer控制，支持明确命名的动画clip
 */

import * as THREE from 'three';

export class AnimationController {
    constructor(kungFuDog) {
        this.kungFuDog = kungFuDog;
        this.mixer = kungFuDog.getMixer();
        
        // 动画状态管理
        this.currentAction = null;
        this.previousAction = null;
        this.actions = new Map(); // 存储所有动画动作
        
        // 动画配置
        this.fadeDuration = 0.15; // 动画切换淡入淡出时间（减少以支持快速交互）
        
        // 初始化动画系统
        this.init();
    }
    
    init() {
        console.log('🎮 初始化动画控制器...');
        
        // 创建所有动画动作
        this.createAllActions();
        
        // 设置默认动画
        this.playAction('stand');
        
        console.log('✅ 动画控制器初始化完成');
    }
    
    createAllActions() {
        if (!this.mixer || !this.kungFuDog.animations) {
            console.warn('⚠️ 动画混合器或动画数据未准备就绪');
            return;
        }

        // 遍历所有可用动画，创建动作
        this.kungFuDog.animations.forEach((clip, name) => {
            const action = this.mixer.clipAction(clip);
            this.actions.set(name, action);

            // 设置动画属性
            action.setEffectiveTimeScale(1);
            action.setEffectiveWeight(1);
        });

        // 如果有真实动画，创建动画名称映射
        if (this.actions.size > 0) {
            this.createAnimationMapping();
        }

        console.log(`🎬 创建了 ${this.actions.size} 个动画动作`);

        // 打印所有可用的动画到控制台
        this.printAllAnimations();
    }

    /**
     * 创建动画名称映射（将通用动作名映射到实际动画名）
     */
    createAnimationMapping() {
        this.animationMapping = new Map();

        // 获取所有动画名称
        const animationNames = Array.from(this.actions.keys());

        // 创建映射关系（基于实际可用的14种动画）
        const mappings = {
            // 基础动作映射
            'stand': ['stand'],  // 站立待机
            'jump': ['jump'],    // 跳跃动作
            'kick': ['big kick', 'Hurricane Kick', 'kick'],  // 踢腿攻击（优先大踢腿和旋风踢）

            // 战斗动作映射（注意实际动画名有空格）
            'block': ['Block '],     // 格挡防御
            'hurt': ['Hurt '],       // 受伤动作
            'leftleghurt': ['Left leg hurt '],  // 左腿受伤

            // 说话动画映射（优先双手说话）
            'talking': ['Talking Two hands ', 'Talking '],

            // 表演和娱乐动画映射
            'dance': ['Dancing chacha', 'Twist Dance'],  // 舞蹈动作
            'taunt': ['taunt'],      // 挑衅动作
            'stretch': ['Arm Stretching'],  // 伸展动作

            // 兼容性映射（保持向后兼容）
            'Block': ['Block '],     // 直接映射（保持大小写）
            'Hurt': ['Hurt '],       // 直接映射
            'Left leg hurt': ['Left leg hurt '],  // 直接映射
            'punch': ['taunt'],      // 没有punch动画，映射到挑衅
            'roll': ['Hurricane Kick'],  // 没有roll动画，映射到旋风踢
            'bow': ['taunt'],        // 没有bow动画，映射到挑衅
            'sit': ['stand'],        // 没有sit动画，保持站立
            'walk': ['stand']        // 没有walk动画，保持站立
        };

        // 为每个通用动作找到最匹配的真实动画（严格匹配模式，自动处理空格）
        Object.entries(mappings).forEach(([genericName, possibleNames]) => {
            for (const possibleName of possibleNames) {
                // 严格的精确匹配（自动去除首尾空格）
                const matchedAnimation = animationNames.find(name =>
                    name.trim().toLowerCase() === possibleName.trim().toLowerCase()
                );

                if (matchedAnimation) {
                    this.animationMapping.set(genericName, matchedAnimation);
                    console.log(`🎯 严格映射动画: ${genericName} -> "${matchedAnimation}"`);
                    break;
                }
            }

            // 如果严格匹配失败，记录警告但不使用默认映射
            if (!this.animationMapping.has(genericName)) {
                console.warn(`⚠️ 未找到严格匹配的动画: ${genericName} (候选: ${possibleNames.join(', ')})`);
                console.log(`🔍 可用动画名称: ${animationNames.map(name => `"${name}"`).join(', ')}`);
            }
        });
    }
    
    /**
     * 播放指定动画
     * @param {string} actionName - 动画名称
     * @param {boolean} loop - 是否循环播放（可选，会根据动画类型自动判断）
     * @param {number} duration - 动画持续时间（秒），0表示使用原始时长
     */
    playAction(actionName, loop = null, duration = 0) {
        console.log(`🎭 播放动画: ${actionName}`);

        // 尝试获取映射的真实动画名
        let realAnimationName = actionName;
        if (this.animationMapping && this.animationMapping.has(actionName)) {
            realAnimationName = this.animationMapping.get(actionName);
            console.log(`🎯 使用严格映射动画: ${actionName} -> ${realAnimationName}`);
        } else {
            console.warn(`⚠️ 未找到动画映射: ${actionName}`);
        }

        // 检查动画是否存在
        if (!this.actions.has(realAnimationName)) {
            console.error(`❌ 动画 "${realAnimationName}" 不存在，无法播放`);
            console.log(`📋 可用动画列表: ${Array.from(this.actions.keys()).join(', ')}`);
            return;
        }

        const newAction = this.actions.get(realAnimationName);

        // 如果是同一个动画，直接返回
        if (this.currentAction === newAction) {
            return;
        }

        // 保存前一个动画
        this.previousAction = this.currentAction;
        this.currentAction = newAction;

        // 智能判断是否循环播放
        const shouldLoop = this.shouldAnimationLoop(actionName, loop);
        console.log(`🔄 动画循环设置: ${actionName} -> ${shouldLoop ? '循环播放' : '播放一次'}`);

        // 设置新动画属性
        newAction.reset();
        newAction.setLoop(shouldLoop ? THREE.LoopRepeat : THREE.LoopOnce);

        // 对于一次性动画，设置 clampWhenFinished 为 true，保持在最后一帧
        if (!shouldLoop) {
            newAction.clampWhenFinished = true;
        }

        if (duration > 0) {
            newAction.setDuration(duration);
        }

        // 如果有前一个动画，进行平滑切换
        if (this.previousAction) {
            this.crossFade(this.previousAction, newAction, this.fadeDuration);
        } else {
            newAction.play();
        }

        // 如果是非循环动画，设置完成后的回调
        if (!shouldLoop) {
            this.setupAnimationFinishedCallback(newAction, actionName);
        }

        // 触发动画事件
        this.onAnimationStart(actionName);
    }

    /**
     * 智能判断动画是否应该循环播放
     * @param {string} actionName - 动画名称
     * @param {boolean|null} explicitLoop - 明确指定的循环设置
     * @returns {boolean} 是否应该循环播放
     */
    shouldAnimationLoop(actionName, explicitLoop) {
        // 如果明确指定了循环设置，使用明确设置
        if (explicitLoop !== null) {
            return explicitLoop;
        }

        // 定义只循环播放的动画（主要是静止状态动画）
        const loopingAnimations = [
            'stand',     // 站立
            'idle',      // 待机
            'standing',  // 站立
            'rest',      // 休息
            'default',   // 默认
            'walk',      // 行走（可以循环）
            'walking',   // 行走
            'run',       // 跑步
            'running'    // 跑步
        ];

        // 定义只播放一次的动画（动作类、受伤类、特效类）
        const oneTimeAnimations = [
            // 受伤动画
            'hurt', 'Hurt', 'injured', 'damage', 'pain',
            'Left leg hurt', 'Right leg hurt', 'Head hurt',

            // 打斗动画
            'punch', 'punching', 'attack', 'hit', 'strike',
            'kick', 'kicking', 'leg_attack',
            'roll', 'rolling', 'dodge', 'tumble',
            'bow', 'bowing', 'greeting', 'salute',
            'block', 'Block', 'defend', 'guard',

            // 动作动画
            'jump', 'jumping', 'leap', 'hop',
            'sit', 'sitting', 'crouch', 'crouching',

            // 特殊动画
            'down', 'fall', 'falling', 'death', 'die'
        ];

        // 检查动画名称（不区分大小写，去除空格）
        const normalizedName = actionName.trim().toLowerCase();

        // 优先检查一次性动画
        if (oneTimeAnimations.some(name => normalizedName === name.toLowerCase())) {
            return false;
        }

        // 检查循环动画
        if (loopingAnimations.some(name => normalizedName === name.toLowerCase())) {
            return true;
        }

        // 默认情况：如果是站立相关的动画，循环播放；其他动画播放一次
        if (normalizedName.includes('stand') || normalizedName.includes('idle')) {
            return true;
        }

        // 默认为播放一次（更安全的选择）
        console.log(`⚠️ 未知动画类型 "${actionName}"，默认设置为播放一次`);
        return false;
    }

    /**
     * 设置动画完成后的回调
     * @param {THREE.AnimationAction} action - 动画动作
     * @param {string} actionName - 动画名称
     */
    setupAnimationFinishedCallback(action, actionName) {
        // 移除之前的监听器（如果有）
        if (action._finishedListener) {
            this.mixer.removeEventListener('finished', action._finishedListener);
        }

        // 创建完成监听器
        action._finishedListener = (event) => {
            if (event.action === action) {
                console.log(`✅ 动画完成: ${actionName}`);
                this.onAnimationFinished(actionName);

                // 移除监听器
                this.mixer.removeEventListener('finished', action._finishedListener);
                action._finishedListener = null;
            }
        };

        // 添加监听器
        this.mixer.addEventListener('finished', action._finishedListener);
    }

    /**
     * 动画完成事件
     * @param {string} actionName - 完成的动画名称
     */
    onAnimationFinished(actionName) {
        console.log(`🎬 动画结束: ${actionName}`);

        // 根据动画类型决定后续行为
        const shouldReturnToStand = this.shouldReturnToStandAfterAnimation(actionName);

        if (shouldReturnToStand) {
            console.log(`🔄 ${actionName} 动画完成，立即切换到站立状态`);
            // 立即切换到站立动画，不延迟
            this.playAction('stand');
        }
    }

    /**
     * 判断动画完成后是否应该返回站立状态
     * @param {string} actionName - 动画名称
     * @returns {boolean} 是否应该返回站立
     */
    shouldReturnToStandAfterAnimation(actionName) {
        // 这些动画完成后应该返回站立状态
        const returnToStandAnimations = [
            'hurt', 'Hurt', 'injured', 'damage', 'pain',
            'Left leg hurt', 'Right leg hurt', 'Head hurt',
            'punch', 'punching', 'attack', 'hit', 'strike',
            'kick', 'kicking', 'leg_attack',
            'roll', 'rolling', 'dodge', 'tumble',
            'bow', 'bowing', 'greeting', 'salute',
            'block', 'Block', 'defend', 'guard',
            'jump', 'jumping', 'leap', 'hop'
        ];

        // 这些动画完成后不需要返回站立（保持当前状态）
        const keepStateAnimations = [
            'sit', 'sitting', 'crouch', 'crouching',
            'down', 'fall', 'falling', 'death', 'die',
            'walk', 'walking', 'run', 'running'
        ];

        const normalizedName = actionName.trim().toLowerCase();

        // 检查是否应该返回站立
        if (returnToStandAnimations.some(name => normalizedName === name.toLowerCase())) {
            return true;
        }

        // 检查是否应该保持当前状态
        if (keepStateAnimations.some(name => normalizedName === name.toLowerCase())) {
            return false;
        }

        // 默认返回站立状态
        return true;
    }

    /**
     * 播放占位动画（用于演示）
     */
    playPlaceholderAnimation(actionName, loop = true, duration = 2) {
        console.log(`🎪 播放占位动画: ${actionName}`);
        
        if (!this.kungFuDog.bodyParts) {
            console.warn('⚠️ 身体部位引用不存在');
            return;
        }
        
        // 根据动画名称播放不同的占位动画
        switch (actionName) {
            case 'stand':
                this.animateStand();
                break;
            case 'walk':
                this.animateWalk();
                break;
            case 'sit':
                this.animateSit();
                break;
            case 'jump':
                this.animateJump();
                break;
            case 'punch':
                this.animatePunch();
                break;
            case 'kick':
                this.animateKick();
                break;
            case 'roll':
                this.animateRoll();
                break;
            case 'bow':
                this.animateBow();
                break;
            default:
                this.animateStand();
        }
        
        this.onAnimationStart(actionName);
    }
    
    // 占位动画实现
    animateStand() {
        const body = this.kungFuDog.bodyParts.body;
        const tail = this.kungFuDog.bodyParts.tail;
        
        // 轻微的呼吸动画
        this.animateFloat(body, 0.05, 2000);
        this.animateWag(tail, 0.2, 1500);
    }
    
    animateWalk() {
        const legs = [
            this.kungFuDog.bodyParts.frontLeftLeg,
            this.kungFuDog.bodyParts.frontRightLeg,
            this.kungFuDog.bodyParts.backLeftLeg,
            this.kungFuDog.bodyParts.backRightLeg
        ];
        
        // 腿部交替运动
        legs.forEach((leg, index) => {
            this.animateWalk(leg, index * 200);
        });
    }
    
    animateSit() {
        const body = this.kungFuDog.bodyParts.body;
        const backLegs = [
            this.kungFuDog.bodyParts.backLeftLeg,
            this.kungFuDog.bodyParts.backRightLeg
        ];
        
        // 身体下降
        this.animateTo(body, { y: 0.8 }, 1000);
        
        // 后腿弯曲
        backLegs.forEach(leg => {
            this.animateTo(leg, { y: 0.2 }, 1000);
        });
    }
    
    animateJump() {
        const body = this.kungFuDog.bodyParts.body;
        
        // 跳跃动画
        this.animateJump(body, 1.5, 1000);
    }
    
    animatePunch() {
        const frontRightLeg = this.kungFuDog.bodyParts.frontRightLeg;
        
        // 出拳动画
        this.animatePunch(frontRightLeg, 0.5, 800);
    }
    
    animateKick() {
        const backRightLeg = this.kungFuDog.bodyParts.backRightLeg;
        
        // 踢腿动画
        this.animateKick(backRightLeg, 0.8, 1000);
    }
    
    animateRoll() {
        const model = this.kungFuDog.model;
        
        // 翻滚动画
        this.animateRoll(model, 2000);
    }
    
    animateBow() {
        const head = this.kungFuDog.bodyParts.head;
        const body = this.kungFuDog.bodyParts.body;
        
        // 鞠躬动画
        this.animateTo(head, { rotationX: 0.5 }, 1000);
        this.animateTo(body, { rotationX: 0.3 }, 1000);
    }
    
    // 动画辅助方法
    animateFloat(object, amplitude, duration) {
        const startY = object.position.y;
        const startTime = Date.now();
        
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = (elapsed % duration) / duration;
            const offset = Math.sin(progress * Math.PI * 2) * amplitude;
            
            object.position.y = startY + offset;
            
            if (elapsed < duration * 3) { // 播放3个周期
                requestAnimationFrame(animate);
            } else {
                object.position.y = startY;
            }
        };
        
        animate();
    }
    
    animateWag(object, amplitude, duration) {
        const startRotation = object.rotation.z;
        const startTime = Date.now();
        
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = (elapsed % duration) / duration;
            const offset = Math.sin(progress * Math.PI * 4) * amplitude;
            
            object.rotation.z = startRotation + offset;
            
            if (elapsed < duration * 2) {
                requestAnimationFrame(animate);
            } else {
                object.rotation.z = startRotation;
            }
        };
        
        animate();
    }
    
    animateTo(object, target, duration) {
        const start = {
            x: object.position.x,
            y: object.position.y,
            z: object.position.z,
            rotationX: object.rotation.x,
            rotationY: object.rotation.y,
            rotationZ: object.rotation.z
        };
        
        const startTime = Date.now();
        
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const eased = this.easeInOutQuad(progress);
            
            // 插值计算
            if (target.x !== undefined) object.position.x = start.x + (target.x - start.x) * eased;
            if (target.y !== undefined) object.position.y = start.y + (target.y - start.y) * eased;
            if (target.z !== undefined) object.position.z = start.z + (target.z - start.z) * eased;
            if (target.rotationX !== undefined) object.rotation.x = start.rotationX + (target.rotationX - start.rotationX) * eased;
            if (target.rotationY !== undefined) object.rotation.y = start.rotationY + (target.rotationY - start.rotationY) * eased;
            if (target.rotationZ !== undefined) object.rotation.z = start.rotationZ + (target.rotationZ - start.rotationZ) * eased;
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        animate();
    }
    
    easeInOutQuad(t) {
        return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
    }
    
    /**
     * 平滑切换动画
     */
    crossFade(fromAction, toAction, duration) {
        toAction.reset();
        toAction.setEffectiveTimeScale(1);
        toAction.setEffectiveWeight(1);
        toAction.crossFadeFrom(fromAction, duration, true);
        toAction.play();
    }
    
    /**
     * 停止当前动画
     */
    stopCurrentAction() {
        if (this.currentAction) {
            this.currentAction.stop();
            this.currentAction = null;
        }
    }
    
    /**
     * 动画开始事件
     */
    onAnimationStart(actionName) {
        console.log(`🎬 动画开始: ${actionName}`);
        
        // 可以在这里添加动画开始时的逻辑
        // 比如触发音效、更新UI等
    }
    
    /**
     * 更新动画系统
     */
    update(deltaTime) {
        if (this.mixer) {
            this.mixer.update(deltaTime);
        }
    }
    
    /**
     * 获取当前播放的动画名称
     */
    getCurrentAnimationName() {
        if (!this.currentAction) return null;
        
        // 从actions Map中找到对应的名称
        for (let [name, action] of this.actions) {
            if (action === this.currentAction) {
                return name;
            }
        }
        
        return null;
    }

    /**
     * 获取当前播放的通用动画名称
     * @returns {string|null} 通用动画名称
     */
    getCurrentGenericAnimationName() {
        const realAnimationName = this.getCurrentAnimationName();
        if (!realAnimationName || !this.animationMapping) return null;

        // 从映射中找到对应的通用名称
        for (let [genericName, mappedName] of this.animationMapping) {
            if (mappedName === realAnimationName) {
                return genericName;
            }
        }

        return null;
    }

    /**
     * 打印所有可用的动画到控制台
     */
    printAllAnimations() {
        console.log('🎭 ===== 所有可用动画列表 =====');

        if (this.actions.size === 0) {
            console.log('❌ 没有找到任何动画');
            return;
        }

        console.log(`📊 总计: ${this.actions.size} 个动画`);
        console.log('─'.repeat(50));

        // 按字母顺序排序并打印
        const sortedAnimations = Array.from(this.actions.keys()).sort();

        sortedAnimations.forEach((animationName, index) => {
            const action = this.actions.get(animationName);
            const clip = action.getClip();

            console.log(`${(index + 1).toString().padStart(2, '0')}. 🎬 "${animationName}"`);
            console.log(`    ├─ 时长: ${clip.duration.toFixed(2)}秒`);
            console.log(`    ├─ 轨道数: ${clip.tracks.length}`);
            console.log(`    ├─ 循环模式: ${this.getLoopModeString(action.loop)}`);
            console.log(`    └─ 当前权重: ${action.weight.toFixed(2)}`);

            if (index < sortedAnimations.length - 1) {
                console.log('');
            }
        });

        console.log('─'.repeat(50));

        // 如果有动画映射，也打印出来
        if (this.animationMapping && this.animationMapping.size > 0) {
            console.log('🔗 动画映射关系:');
            this.animationMapping.forEach((realName, genericName) => {
                console.log(`    ${genericName} → ${realName}`);
            });
            console.log('─'.repeat(50));
        }

        console.log('✅ 动画列表打印完成');
    }

    /**
     * 获取循环模式的字符串描述
     */
    getLoopModeString(loopMode) {
        switch (loopMode) {
            case THREE.LoopOnce:
                return '播放一次';
            case THREE.LoopRepeat:
                return '重复循环';
            case THREE.LoopPingPong:
                return '来回循环';
            default:
                return '未知模式';
        }
    }



    /**
     * 修复动画高度不一致问题
     */
    fixAnimationHeights() {
        if (!this.kungFuDog || !this.kungFuDog.model) {
            console.warn('⚠️ 无法修复动画高度：模型未加载');
            return false;
        }

        const armature = this.kungFuDog.model.getObjectByName('Armature');
        if (!armature) {
            console.warn('⚠️ 未找到 Armature');
            return false;
        }

        // 设置统一的基准高度
        const baseHeight = 0; // 可以根据需要调整
        armature.position.y = baseHeight;

        return true;
    }

    /**
     * 清理资源
     */
    dispose() {
        this.stopCurrentAction();
        this.actions.clear();
    }
}
