/**
 * 功夫狗行为系统
 * 管理角色的各种行为逻辑，包括自动行为、情绪状态、行为链等
 */

import SpeechManager from './SpeechManager.js';

export class BehaviorSystem {
    constructor(character, animationController, speechSystem, audioSystem) {
        this.character = character;
        this.animationController = animationController;
        this.speechSystem = speechSystem;
        this.audioSystem = audioSystem;

        // 行为状态
        this.currentBehavior = null;
        this.behaviorQueue = [];
        this.isExecutingBehavior = false;

        // 情绪系统现在由 KungFuDog 角色类管理
        // 行为系统负责根据情绪状态计算行为权重

        // 台词管理系统
        this.speechManager = new SpeechManager();
        this.initializeSpeechManager();
        
        // 自动行为配置
        this.autoBehavior = {
            enabled: true,
            interval: 8000,        // 8秒间隔
            lastAction: 0,
            idleThreshold: 15000,  // 15秒无交互后触发idle行为
            lastInteraction: Date.now(),
            intervalId: null       // 用于清理定时器
        };
        
        // 行为定义
        this.behaviors = this.initializeBehaviors();
        
        // 行为历史（用于避免重复）
        this.behaviorHistory = [];
        this.maxHistoryLength = 5;
        
        console.log('🎭 行为系统初始化完成');
        this.startAutoBehavior();
    }

    /**
     * 初始化台词管理器
     */
    async initializeSpeechManager() {
        try {
            await this.speechManager.loadSpeeches('zh-CN');
            console.log('🗣️ 台词管理器初始化完成');
        } catch (error) {
            console.error('❌ 台词管理器初始化失败:', error);
        }
    }

    /**
     * 初始化行为定义
     */
    initializeBehaviors() {
        return {
            // 基础行为
            idle: {
                name: 'idle',
                type: 'basic',
                actions: ['stand'],
                speechCategory: 'behaviors',
                speechType: 'idle',
                weight: 1.0,
                cooldown: 5000
            },
            
            // 友好行为
            greeting: {
                name: 'greeting',
                type: 'social',
                actions: ['Talking Two hands', 'stand'],
                speechCategory: 'behaviors',
                speechType: 'greeting',
                weight: 0.8,
                cooldown: 10000
            },

            // 展示行为
            showOff: {
                name: 'showOff',
                type: 'performance',
                actions: ['Hurricane Kick', 'big kick', 'stand'],
                speechCategory: 'behaviors',
                speechType: 'showOff',
                weight: 0.6,
                cooldown: 15000
            },
            
            // 思考行为
            contemplation: {
                name: 'contemplation',
                type: 'mental',
                actions: ['Arm Stretching', 'stand'],
                speechCategory: 'behaviors',
                speechType: 'martialEthics',
                weight: 0.4,
                cooldown: 20000
            },

            // 活跃行为
            energetic: {
                name: 'energetic',
                type: 'active',
                actions: ['jump', 'Twist Dance', 'stand'],
                speechCategory: 'behaviors',
                speechType: 'active',
                weight: 0.7,
                cooldown: 12000
            },

            // 警觉行为
            alert: {
                name: 'alert',
                type: 'defensive',
                actions: ['Block', 'stand'],
                speechCategory: 'behaviors',
                speechType: 'defensive',
                weight: 0.3,
                cooldown: 18000
            },

            // 🔥 新增：攻击性行为
            aggressive: {
                name: 'aggressive',
                type: 'aggressive',
                actions: ['big kick', 'Hurricane Kick', 'kick', 'stand'],
                speechCategory: 'behaviors',
                speechType: 'aggressive',
                weight: 0.5,
                cooldown: 15000
            },

            fury: {
                name: 'fury',
                type: 'aggressive',
                actions: ['Hurricane Kick', 'big kick', 'taunt', 'stand'],
                speechCategory: 'behaviors',
                speechType: 'aggressive',
                weight: 0.6,
                cooldown: 18000
            },

            // 🎮 新增：玩乐行为
            playful: {
                name: 'playful',
                type: 'playful',
                actions: ['Dancing chacha', 'Twist Dance', 'jump', 'stand'],
                speechCategory: 'behaviors',
                speechType: 'playful',
                weight: 0.6,
                cooldown: 14000
            },

            dance: {
                name: 'dance',
                type: 'playful',
                actions: ['Dancing chacha', 'Twist Dance', 'stand'],
                speechCategory: 'behaviors',
                speechType: 'playful',
                weight: 0.7,
                cooldown: 16000
            },

            playfulKick: {
                name: 'playfulKick',
                type: 'playful',
                actions: ['kick', 'jump', 'Twist Dance', 'stand'],
                speechCategory: 'behaviors',
                speechType: 'playful',
                weight: 0.5,
                cooldown: 12000
            },

            // 😏 新增：贱气行为
            taunt: {
                name: 'taunt',
                type: 'snarky',
                actions: ['taunt'],
                speechCategory: 'behaviors',
                speechType: 'taunt',
                weight: 0.5,
                cooldown: 8000
            },

            mockery: {
                name: 'mockery',
                type: 'snarky',
                actions: ['big kick', 'taunt', 'Talking', 'stand'],
                speechCategory: 'behaviors',
                speechType: 'mockery',
                weight: 0.6,
                cooldown: 10000
            },

            provoke: {
                name: 'provoke',
                type: 'snarky',
                actions: ['Hurricane Kick', 'taunt', 'stand'],
                speechCategory: 'behaviors',
                speechType: 'provoke',
                weight: 0.4,
                cooldown: 12000
            },

            // 😨 新增：恐惧行为
            fearful: {
                name: 'fearful',
                type: 'fearful',
                actions: ['Block', 'Hurt', 'stand'],
                speechCategory: 'behaviors',
                speechType: 'fearful',
                weight: 0.4,
                cooldown: 12000
            },

            retreat: {
                name: 'retreat',
                type: 'fearful',
                actions: ['Block', 'Block', 'stand'],
                speechCategory: 'behaviors',
                speechType: 'fearful',
                weight: 0.5,
                cooldown: 15000
            },

            nervous: {
                name: 'nervous',
                type: 'fearful',
                actions: ['Hurt', 'Arm Stretching', 'stand'],
                speechCategory: 'behaviors',
                speechType: 'fearful',
                weight: 0.3,
                cooldown: 10000
            }
        };
    }
    
    /**
     * 启动自动行为系统
     */
    startAutoBehavior() {
        if (!this.autoBehavior.enabled) return;

        // 清理之前的定时器
        this.stopAutoBehavior();

        const checkBehavior = () => {
            if (!this.autoBehavior.enabled) return;

            const now = Date.now();
            const timeSinceLastAction = now - this.autoBehavior.lastAction;
            const timeSinceLastInteraction = now - this.autoBehavior.lastInteraction;

            // 检查是否需要触发自动行为
            if (timeSinceLastAction >= this.autoBehavior.interval && !this.isExecutingBehavior) {
                // 根据无交互时间选择行为类型
                if (timeSinceLastInteraction >= this.autoBehavior.idleThreshold) {
                    this.triggerBehavior('idle');
                } else {
                    this.triggerRandomBehavior();
                }
                this.autoBehavior.lastAction = now;
            }
        };

        // 使用 setInterval 而不是递归 setTimeout
        this.autoBehavior.intervalId = setInterval(checkBehavior, 1000);

        // 延迟首次执行
        setTimeout(() => {
            if (this.autoBehavior.enabled) {
                checkBehavior();
            }
        }, this.autoBehavior.interval);

        console.log('🤖 自动行为系统已启动');
    }

    /**
     * 停止自动行为系统
     */
    stopAutoBehavior() {
        if (this.autoBehavior.intervalId) {
            clearInterval(this.autoBehavior.intervalId);
            this.autoBehavior.intervalId = null;
            console.log('🛑 自动行为系统已停止');
        }
    }
    
    /**
     * 触发指定行为
     */
    async triggerBehavior(behaviorName, force = false) {
        const behavior = this.behaviors[behaviorName];
        if (!behavior) {
            console.warn(`⚠️ 未找到行为: ${behaviorName}`);
            return false;
        }
        
        // 检查冷却时间
        if (!force && this.isBehaviorOnCooldown(behaviorName)) {
            console.log(`⏰ 行为 ${behaviorName} 仍在冷却中`);
            return false;
        }
        
        // 检查是否正在执行其他行为
        if (this.isExecutingBehavior && !force) {
            this.behaviorQueue.push(behaviorName);
            console.log(`📋 行为 ${behaviorName} 已加入队列`);
            return false;
        }
        
        return await this.executeBehavior(behavior);
    }
    
    /**
     * 执行行为
     */
    async executeBehavior(behavior) {
        this.isExecutingBehavior = true;
        this.currentBehavior = behavior;
        
        console.log(`🎭 执行行为: ${behavior.name}`);
        
        try {
            // 选择语音
            const speech = this.getSpeechForBehavior(behavior);
            
            // 执行动作序列
            for (let i = 0; i < behavior.actions.length; i++) {
                const action = behavior.actions[i];
                
                // 播放动画
                if (this.animationController) {
                    this.animationController.playAction(action);
                }
                
                // 在第一个动作时播放语音
                if (i === 0 && speech && this.speechSystem) {
                    this.speechSystem.speak(speech, this.character);
                }
                
                // 播放音效
                this.playBehaviorSound(behavior.type, action);
                
                // 等待动作完成（如果不是最后一个动作）
                if (i < behavior.actions.length - 1) {
                    await this.waitForAction(action);
                }
            }
            
            // 更新情绪状态
            this.updateEmotionalState(behavior);
            
            // 记录行为历史
            this.addToBehaviorHistory(behavior.name);
            
            // 设置冷却时间
            this.setBehaviorCooldown(behavior.name, behavior.cooldown);
            
            console.log(`✅ 行为 ${behavior.name} 执行完成`);
            return true;
            
        } catch (error) {
            console.error(`❌ 行为执行失败:`, error);
            return false;
        } finally {
            this.isExecutingBehavior = false;
            this.currentBehavior = null;
            
            // 处理队列中的下一个行为
            this.processNextBehavior();
        }
    }
    
    /**
     * 触发随机行为
     */
    triggerRandomBehavior() {
        const availableBehaviors = this.getAvailableBehaviors();
        if (availableBehaviors.length === 0) {
            console.log('📭 没有可用的行为');
            return;
        }
        
        // 根据权重和情绪状态选择行为
        const selectedBehavior = this.selectBehaviorByWeight(availableBehaviors);
        this.triggerBehavior(selectedBehavior.name);
    }
    
    /**
     * 获取可用行为列表
     */
    getAvailableBehaviors() {
        return Object.values(this.behaviors).filter(behavior => {
            // 过滤冷却中的行为
            if (this.isBehaviorOnCooldown(behavior.name)) return false;
            
            // 避免重复最近的行为
            if (this.behaviorHistory.includes(behavior.name)) return false;
            
            return true;
        });
    }
    
    /**
     * 根据权重选择行为
     */
    selectBehaviorByWeight(behaviors) {
        // 根据情绪状态调整权重
        const adjustedBehaviors = behaviors.map(behavior => ({
            ...behavior,
            adjustedWeight: this.calculateAdjustedWeight(behavior)
        }));

        // 调试：显示所有行为的权重
        console.log('🎯 行为权重分析:');
        adjustedBehaviors.forEach(behavior => {
            console.log(`  ${behavior.name} (${behavior.type}): 基础权重${behavior.weight} → 调整后权重${behavior.adjustedWeight.toFixed(3)}`);
        });

        // 权重随机选择
        const totalWeight = adjustedBehaviors.reduce((sum, b) => sum + b.adjustedWeight, 0);
        let random = Math.random() * totalWeight;

        console.log(`🎲 总权重: ${totalWeight.toFixed(3)}, 随机值: ${random.toFixed(3)}`);

        for (const behavior of adjustedBehaviors) {
            random -= behavior.adjustedWeight;
            if (random <= 0) {
                console.log(`✅ 选中行为: ${behavior.name} (${behavior.type})`);
                return behavior;
            }
        }

        // 备选方案
        console.log(`⚠️ 使用备选方案: ${adjustedBehaviors[0].name}`);
        return adjustedBehaviors[0];
    }
    
    /**
     * 计算调整后的权重
     */
    calculateAdjustedWeight(behavior) {
        let weight = behavior.weight;

        // 使用情绪权重修正
        const modifier = this.getEmotionalWeightModifier(behavior.type);
        weight *= modifier;

        return Math.max(0.1, weight); // 最小权重0.1
    }

    /**
     * 获取行为类型的情绪加权修正值
     * @param {string} behaviorType - 行为类型
     * @returns {number} 权重修正系数
     */
    getEmotionalWeightModifier(behaviorType) {
        // 获取角色的情绪状态
        const emotionalState = this.character && this.character.getEmotionalState ?
            this.character.getEmotionalState() : null;

        if (!emotionalState) {
            return 1.0; // 如果没有情绪状态，返回默认权重
        }

        switch (behaviorType) {
            case 'social':
                return emotionalState.friendliness * (1 - emotionalState.anger * 0.5);
            case 'active':
                return emotionalState.energy * (1 - emotionalState.fear * 0.3);
            case 'performance':
                return emotionalState.happiness * emotionalState.playfulness;
            case 'defensive':
                return emotionalState.alertness * (1 + emotionalState.fear * 0.4);
            case 'aggressive':
                return emotionalState.anger * emotionalState.energy;
            case 'playful':
                return emotionalState.playfulness * emotionalState.happiness;
            case 'snarky':
                return emotionalState.snark * (1 + emotionalState.playfulness * 0.3);
            case 'fearful':
                return emotionalState.fear * emotionalState.alertness;
            default:
                // 基础行为受整体情绪平衡影响
                const positiveEmotions = (emotionalState.happiness + emotionalState.playfulness + emotionalState.friendliness) / 3;
                const negativeEmotions = (emotionalState.anger + emotionalState.fear) / 2;
                return Math.max(0.1, positiveEmotions - negativeEmotions * 0.3);
        }
    }

    /**
     * 根据情绪和行为类型选择适当行为
     * @returns {Object|null} 选择的行为对象
     */
    selectBehaviorByEmotion() {
        // 获取主导情绪
        const dominantEmotion = this.character && this.character.getDominantEmotion ?
            this.character.getDominantEmotion() : 'happiness';

        // 根据主导情绪选择行为类型
        let preferredBehaviorType = this.mapEmotionToBehaviorType(dominantEmotion);

        // 获取该类型的行为
        const availableBehaviors = Object.values(this.behaviors).filter(
            behavior => behavior.type === preferredBehaviorType
        );

        if (availableBehaviors.length > 0) {
            const weight = this.getEmotionalWeightModifier(preferredBehaviorType);
            console.log(`🎭 基于主导情绪 ${dominantEmotion} 选择行为类型：${preferredBehaviorType}, 权重：${weight.toFixed(2)}`);

            return this.selectBehaviorByWeight(availableBehaviors);
        }

        return null;
    }

    /**
     * 将情绪映射到行为类型
     * @param {string} emotion - 情绪名称
     * @returns {string} 行为类型
     */
    mapEmotionToBehaviorType(emotion) {
        const emotionToBehaviorMap = {
            happiness: 'social',
            energy: 'active',
            alertness: 'defensive',
            friendliness: 'social',
            anger: 'aggressive',
            fear: 'fearful',
            snark: 'snarky',
            playfulness: 'playful'
        };

        return emotionToBehaviorMap[emotion] || 'basic';
    }
    
    /**
     * 更新情绪状态
     */
    updateEmotionalState(behavior) {
        // 使用角色的情绪系统更新情绪状态
        if (this.character && this.character.updateEmotionalState) {
            this.character.updateEmotionalState(behavior.type);
        }
    }
    
    /**
     * 应用情绪自然衰减（现在由角色类管理）
     */
    applyEmotionalDecay() {
        // 情绪衰减现在由 KungFuDog 的 update 方法自动处理
        // 这个方法保留为兼容性，但不执行任何操作
    }
    
    /**
     * 为行为获取台词
     * @param {Object} behavior - 行为对象
     * @returns {string|null} 台词内容
     */
    getSpeechForBehavior(behavior) {
        // 优先使用台词管理器
        if (behavior.speechCategory && behavior.speechType) {
            try {
                return this.speechManager.getSpeech(behavior.speechCategory, behavior.speechType);
            } catch (error) {
                console.warn('⚠️ 台词管理器获取台词失败:', error);
            }
        }

        // 后备方案：使用传统的 speeches 数组
        if (behavior.speeches && behavior.speeches.length > 0) {
            return this.selectRandomSpeech(behavior.speeches);
        }

        // 根据行为类型获取默认台词
        return this.getDefaultSpeechForBehaviorType(behavior.type);
    }

    /**
     * 选择随机语音（后备方案）
     */
    selectRandomSpeech(speeches) {
        if (!speeches || speeches.length === 0) return null;
        return speeches[Math.floor(Math.random() * speeches.length)];
    }

    /**
     * 根据行为类型获取默认台词
     * @param {string} behaviorType - 行为类型
     * @returns {string} 默认台词
     */
    getDefaultSpeechForBehaviorType(behaviorType) {
        const defaultSpeeches = {
            'basic': '...',
            'social': '很高兴见到你！',
            'performance': '看我的功夫！',
            'mental': '武功的真谛在于内心的修炼...',
            'active': '感觉精力充沛！',
            'defensive': '保持警觉！',
            'aggressive': '接招！',
            'playful': '哈哈，好玩！',
            'snarky': '哼，就这点本事？',
            'fearful': '有点紧张...'
        };

        return defaultSpeeches[behaviorType] || '...';
    }
    
    /**
     * 播放行为音效
     */
    playBehaviorSound(behaviorType, action) {
        if (!this.audioSystem) return;

        // 根据动作直接播放对应音效
        if (this.audioSystem.sounds && this.audioSystem.sounds.has(action)) {
            this.audioSystem.playSound(action);
            return;
        }

        // 根据行为类型播放相应音效
        const soundMap = {
            'performance': 'kick',  // 使用存在的音效
            'active': 'jump',       // 使用存在的音效
            'defensive': 'punch',   // 使用存在的音效
            'social': 'bow'         // 使用存在的音效
        };

        const soundType = soundMap[behaviorType];
        if (soundType) {
            this.audioSystem.playSound(soundType);
        }
    }
    
    /**
     * 等待动作完成
     */
    async waitForAction(action) {
        // 根据动作类型设置等待时间
        const actionDurations = {
            'stand': 1000,
            'jump': 2000,
            'kick': 1500,
            'bow': 2000
        };
        
        const duration = actionDurations[action] || 1000;
        return new Promise(resolve => setTimeout(resolve, duration));
    }
    
    /**
     * 处理队列中的下一个行为
     */
    processNextBehavior() {
        if (this.behaviorQueue.length > 0) {
            const nextBehavior = this.behaviorQueue.shift();
            setTimeout(() => {
                this.triggerBehavior(nextBehavior);
            }, 1000); // 1秒延迟
        }
    }
    
    /**
     * 检查行为是否在冷却中
     */
    isBehaviorOnCooldown(behaviorName) {
        const cooldownKey = `behavior_${behaviorName}_cooldown`;
        const cooldownEnd = localStorage.getItem(cooldownKey);
        if (!cooldownEnd) return false;
        
        return Date.now() < parseInt(cooldownEnd);
    }
    
    /**
     * 设置行为冷却时间
     */
    setBehaviorCooldown(behaviorName, cooldown) {
        const cooldownKey = `behavior_${behaviorName}_cooldown`;
        const cooldownEnd = Date.now() + cooldown;
        localStorage.setItem(cooldownKey, cooldownEnd.toString());
    }
    
    /**
     * 添加到行为历史
     */
    addToBehaviorHistory(behaviorName) {
        this.behaviorHistory.push(behaviorName);
        if (this.behaviorHistory.length > this.maxHistoryLength) {
            this.behaviorHistory.shift();
        }
    }
    
    /**
     * 记录用户交互
     */
    recordInteraction() {
        this.autoBehavior.lastInteraction = Date.now();
        console.log('👆 用户交互已记录');
    }
    
    /**
     * 设置情绪状态（委托给角色类）
     */
    setEmotionalState(emotion, value) {
        if (this.character && this.character.setEmotionalState) {
            return this.character.setEmotionalState(emotion, value);
        }
        console.warn('⚠️ 角色对象不支持情绪系统');
        return false;
    }

    /**
     * 获取当前情绪状态（委托给角色类）
     */
    getEmotionalState() {
        if (this.character && this.character.getEmotionalState) {
            return this.character.getEmotionalState();
        }
        console.warn('⚠️ 角色对象不支持情绪系统');
        return {};
    }
    
    /**
     * 启用/禁用自动行为
     */
    setAutoBehaviorEnabled(enabled) {
        this.autoBehavior.enabled = enabled;
        console.log(`🤖 自动行为系统: ${enabled ? '启用' : '禁用'}`);
    }
    
    /**
     * 清空行为队列
     */
    clearBehaviorQueue() {
        this.behaviorQueue = [];
        console.log('🗑️ 行为队列已清空');
    }
    
    /**
     * 获取系统状态
     */
    getSystemStatus() {
        return {
            currentBehavior: this.currentBehavior?.name || null,
            isExecuting: this.isExecutingBehavior,
            queueLength: this.behaviorQueue.length,
            emotionalState: this.getEmotionalState(),
            autoBehaviorEnabled: this.autoBehavior.enabled,
            behaviorHistory: [...this.behaviorHistory]
        };
    }
    
    /**
     * 功夫表演序列
     */
    async performKungfuDemo() {
        console.log('🥋 开始功夫表演...');

        const demoSequence = [
            { action: 'bow', speech: '各位观众，功夫表演开始！' },
            { action: 'punch', speech: '第一式：猛虎出山！' },
            { action: 'kick', speech: '第二式：神龙摆尾！' },
            { action: 'jump', speech: '第三式：鹤立鸡群！' },
            { action: 'roll', speech: '第四式：懒驴打滚！' },
            { action: 'bow', speech: '表演结束，谢谢大家！' }
        ];

        // 创建表演行为
        const demoBehavior = {
            name: 'kungfuDemo',
            type: 'performance',
            actions: demoSequence.map(step => step.action),
            speeches: demoSequence.map(step => step.speech),
            weight: 1.0,
            cooldown: 30000
        };

        // 执行表演
        return await this.executeDemoBehavior(demoBehavior, demoSequence);
    }

    /**
     * 执行表演行为
     */
    async executeDemoBehavior(behavior, sequence) {
        this.isExecutingBehavior = true;
        this.currentBehavior = behavior;

        try {
            for (let i = 0; i < sequence.length; i++) {
                const step = sequence[i];

                // 播放动画
                if (this.animationController) {
                    this.animationController.playAction(step.action);
                }

                // 播放语音
                if (step.speech && this.speechSystem) {
                    setTimeout(() => {
                        this.speechSystem.speak(step.speech, this.character);
                    }, 300);
                }

                // 播放音效
                this.playBehaviorSound(behavior.type, step.action);

                // 等待动作完成（除了最后一个）
                if (i < sequence.length - 1) {
                    await this.waitForAction(step.action);
                    await new Promise(resolve => setTimeout(resolve, 500)); // 额外间隔
                }
            }

            console.log('✅ 功夫表演完成');
            return true;

        } catch (error) {
            console.error('❌ 功夫表演失败:', error);
            return false;
        } finally {
            this.isExecutingBehavior = false;
            this.currentBehavior = null;
        }
    }

    /**
     * 销毁行为系统
     */
    dispose() {
        this.stopAutoBehavior();
        this.setAutoBehaviorEnabled(false);
        this.clearBehaviorQueue();
        console.log('🗑️ 行为系统已销毁');
    }
}
