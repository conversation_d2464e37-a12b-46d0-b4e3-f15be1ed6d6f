/**
 * 说话系统
 * 负责管理功夫狗的对话显示和嘴型同步动画
 * 符合规则文件要求：文本气泡显示，同步嘴型动画，拟人化宠物口吻
 */

export class SpeechSystem {
    constructor() {
        // DOM元素
        this.speechBubble = document.getElementById('speech-bubble');
        
        // 说话状态
        this.isSpeaking = false;
        this.currentText = '';
        this.speechQueue = []; // 对话队列
        
        // 嘴型动画
        this.mouthAnimation = null;
        this.mouthAnimationId = null;

        // 说话动画状态
        this.isPlayingTalkingAnimation = false;
        
        // 配置
        this.config = {
            typingSpeed: 50, // 打字速度（毫秒/字符）
            displayDuration: 3000, // 显示持续时间
            mouthAnimationSpeed: 200, // 嘴型动画速度
            fadeInDuration: 300,
            fadeOutDuration: 300
        };
        
        // 预设对话内容
        this.dialogues = {
            greeting: [
                "大家好，我是功夫狗！",
                "汪汪！准备好看我的功夫了吗？",
                "今天天气真不错，适合练功！"
            ],
            kungfu: [
                "看我功夫如何！",
                "这招叫做'狗拳十八式'！",
                "功夫不是用来欺负人的！",
                "武功再高，也要讲武德！"
            ],
            praise: [
                "哈哈，我厉害吧！",
                "这还只是我的基础功夫呢！",
                "想学吗？我可以教你！"
            ],
            wisdom: [
                "武功再高，也要讲武德！",
                "练武之人，当以德为先！",
                "功夫是用来保护弱小的！",
                "心中有爱，拳中有情！"
            ]
        };
        
        console.log('🗣️ 说话系统初始化完成');
    }
    
    /**
     * 说话主方法
     * @param {string} text - 要说的文字
     * @param {Object} character - 角色对象（用于嘴型动画）
     * @param {string} category - 对话类别（可选）
     */
    speak(text, character = null, category = null) {
        // 如果没有提供文本，从预设对话中随机选择
        if (!text && category && this.dialogues[category]) {
            const dialogueList = this.dialogues[category];
            text = dialogueList[Math.floor(Math.random() * dialogueList.length)];
        }
        
        if (!text) {
            console.warn('⚠️ 没有提供说话内容');
            return;
        }
        
        console.log(`🗣️ 功夫狗说话: "${text}"`);
        
        // 如果正在说话，加入队列
        if (this.isSpeaking) {
            this.speechQueue.push({ text, character, category });
            return;
        }
        
        // 开始说话
        this.startSpeaking(text, character);
    }
    
    /**
     * 开始说话流程
     */
    async startSpeaking(text, character) {
        this.isSpeaking = true;
        this.currentText = text;
        
        try {
            // 1. 显示气泡
            await this.showSpeechBubble();

            // 2. 开始嘴型动画（同时可能触发talking动画）
            this.startMouthAnimation(character);

            // 3. 打字效果显示文本
            await this.typeText(text);

            // 4. 保持显示一段时间
            await this.wait(this.config.displayDuration);

            // 5. 停止嘴型动画
            this.stopMouthAnimation(character);

            // 6. 停止说话动画
            this.stopTalkingAnimation(character);

            // 7. 隐藏气泡
            await this.hideSpeechBubble();
            
        } catch (error) {
            console.error('❌ 说话过程出错:', error);
        } finally {
            this.isSpeaking = false;
            this.currentText = '';
            
            // 处理队列中的下一个对话
            this.processQueue();
        }
    }
    
    /**
     * 显示对话气泡
     */
    showSpeechBubble() {
        return new Promise((resolve) => {
            this.speechBubble.style.display = 'block';
            this.speechBubble.style.opacity = '0';
            this.speechBubble.innerHTML = '';
            
            // 淡入动画
            let opacity = 0;
            const fadeIn = () => {
                opacity += 0.1;
                this.speechBubble.style.opacity = opacity;
                
                if (opacity < 1) {
                    requestAnimationFrame(fadeIn);
                } else {
                    resolve();
                }
            };
            
            fadeIn();
        });
    }
    
    /**
     * 隐藏对话气泡
     */
    hideSpeechBubble() {
        return new Promise((resolve) => {
            let opacity = 1;
            const fadeOut = () => {
                opacity -= 0.1;
                this.speechBubble.style.opacity = opacity;
                
                if (opacity > 0) {
                    requestAnimationFrame(fadeOut);
                } else {
                    this.speechBubble.style.display = 'none';
                    resolve();
                }
            };
            
            fadeOut();
        });
    }
    
    /**
     * 打字效果显示文本
     */
    typeText(text) {
        return new Promise((resolve) => {
            let index = 0;
            const type = () => {
                if (index < text.length) {
                    this.speechBubble.innerHTML += text.charAt(index);
                    index++;
                    setTimeout(type, this.config.typingSpeed);
                } else {
                    resolve();
                }
            };
            
            type();
        });
    }
    
    /**
     * 开始嘴型动画
     */
    startMouthAnimation(character) {
        if (!character) {
            console.log('💬 没有提供角色对象');
            return;
        }

        // 检查角色是否支持新的Shape Keys嘴型动画
        if (character.mouthShapeKeys &&
            character.mouthShapeKeys.closedIndex !== -1 &&
            character.mouthShapeKeys.openIndex !== -1) {

            console.log('👄 开始Shape Keys嘴型动画，有概率触发talking动画');
            character.startMouthAnimation();

            // 在Shape Keys嘴型动画开始时，有概率触发talking动画
            this.startTalkingAnimation(character);

            return;
        }

        // 回退到旧的嘴型动画系统（如果有bodyParts）
        if (character.bodyParts && character.bodyParts.nose) {
            console.log('👄 开始传统嘴型动画（鼻子缩放）');

            const nose = character.bodyParts.nose;
            const originalScale = nose.scale.clone();
            let isOpen = false;

            const animate = () => {
                if (!this.isSpeaking) return;

                isOpen = !isOpen;
                const scale = isOpen ? 1.3 : 1.0;

                // 平滑过渡
                const targetScale = originalScale.clone().multiplyScalar(scale);
                this.animateScale(nose, targetScale, this.config.mouthAnimationSpeed / 2);

                this.mouthAnimationId = setTimeout(animate, this.config.mouthAnimationSpeed);
            };

            animate();
        } else {
            console.log('💬 角色不支持嘴型动画，仅显示文本');
        }
    }
    
    /**
     * 停止嘴型动画
     */
    stopMouthAnimation(character = null) {
        // 停止传统动画系统
        if (this.mouthAnimationId) {
            clearTimeout(this.mouthAnimationId);
            this.mouthAnimationId = null;
        }

        // 停止Shape Keys动画系统
        if (character && character.stopMouthAnimation) {
            character.stopMouthAnimation();
        }

        console.log('👄 停止嘴型动画');
    }

    /**
     * 开始说话动画（如果在idle状态）
     * @param {Object} character - 角色对象
     */
    startTalkingAnimation(character) {
        if (!character || !character.animationController) {
            return;
        }

        // 检查当前是否在idle状态
        const currentAnimation = character.animationController.getCurrentGenericAnimationName();
        const isIdle = !currentAnimation ||
                      currentAnimation === 'stand' ||
                      currentAnimation === 'idle';

        if (isIdle) {
            // 固定概率触发说话动画
            const talkingProbability = 0.4; // 40%概率
            const shouldPlayTalking = Math.random() < talkingProbability;

            if (shouldPlayTalking) {
                console.log('🗣️ 开始说话动画');
                character.animationController.playAction('talking', false, 0);
                this.isPlayingTalkingAnimation = true;
            } else {
                console.log('🗣️ 保持idle状态说话');
                this.isPlayingTalkingAnimation = false;
            }
        } else {
            console.log(`🗣️ 非idle状态(${currentAnimation})，不播放说话动画`);
            this.isPlayingTalkingAnimation = false;
        }
    }

    /**
     * 停止说话动画
     * @param {Object} character - 角色对象
     */
    stopTalkingAnimation(character) {
        if (this.isPlayingTalkingAnimation && character && character.animationController) {
            console.log('🗣️ 停止说话动画，回到idle状态');
            character.animationController.playAction('stand');
            this.isPlayingTalkingAnimation = false;
        }
    }
    
    /**
     * 缩放动画辅助方法
     */
    animateScale(object, targetScale, duration) {
        const startScale = object.scale.clone();
        const startTime = Date.now();
        
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            object.scale.lerpVectors(startScale, targetScale, progress);
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        animate();
    }
    
    /**
     * 等待指定时间
     */
    wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 处理对话队列
     */
    processQueue() {
        if (this.speechQueue.length > 0) {
            const next = this.speechQueue.shift();
            setTimeout(() => {
                this.speak(next.text, next.character, next.category);
            }, 500); // 短暂延迟
        }
    }
    
    /**
     * 随机说话
     */
    speakRandom(character, category = null) {
        const categories = category ? [category] : Object.keys(this.dialogues);
        const randomCategory = categories[Math.floor(Math.random() * categories.length)];
        this.speak(null, character, randomCategory);
    }
    
    /**
     * 打招呼
     */
    greet(character) {
        this.speak(null, character, 'greeting');
    }
    
    /**
     * 展示功夫
     */
    showKungfu(character) {
        this.speak(null, character, 'kungfu');
    }
    
    /**
     * 夸奖自己
     */
    praise(character) {
        this.speak(null, character, 'praise');
    }
    
    /**
     * 说武德
     */
    speakWisdom(character) {
        this.speak(null, character, 'wisdom');
    }
    
    /**
     * 清空对话队列
     */
    clearQueue() {
        this.speechQueue = [];
    }
    
    /**
     * 立即停止说话
     * @param {Object} character - 角色对象
     */
    stopSpeaking(character = null) {
        this.isSpeaking = false;
        this.stopMouthAnimation(character);
        this.stopTalkingAnimation(character);
        this.hideSpeechBubble();
        this.clearQueue();
    }
    
    /**
     * 检查是否正在说话
     */
    isSpeakingNow() {
        return this.isSpeaking;
    }
    
    /**
     * 获取当前说话内容
     */
    getCurrentText() {
        return this.currentText;
    }
    
    /**
     * 添加自定义对话
     */
    addDialogue(category, dialogues) {
        if (!this.dialogues[category]) {
            this.dialogues[category] = [];
        }
        
        if (Array.isArray(dialogues)) {
            this.dialogues[category].push(...dialogues);
        } else {
            this.dialogues[category].push(dialogues);
        }
    }
    
    /**
     * 清理资源
     */
    dispose() {
        this.stopSpeaking();
        this.clearQueue();
    }
}
