/**
 * 功夫狗角色类
 * 负责加载和管理功夫狗3D模型，包括骨骼绑定和基础设置
 */

import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import EmotionState from '../systems/EmotionState.js';

export class KungFuDog {
    constructor() {
        this.model = null;
        this.mixer = null;
        this.animations = new Map();
        this.currentAction = null;

        // 表情系统
        this.morphTargets = null;
        this.mouthOpenValue = 0;
        this.blinkMesh = null;
        this.blinkIndex = -1;

        // 嘴型动画系统
        this.mouthShapeKeys = null;
        this.isMouthAnimating = false;
        this.mouthAnimationStartTime = 0;

        // 眨眼系统
        this.blinkSystem = {
            enabled: true,
            interval: 3000,
            duration: 200,
            lastBlink: 0,
            isBlinking: false,
            blinkAnimationId: null
        };

        this.loader = new GLTFLoader();
        this.isLoaded = false;
        this.isReady = false;

        // Armature 碰撞体系统（支持多个碰撞盒）
        this.armatureCollision = {
            armature: null,
            boundingBox: null,
            collisionMeshes: [], // 改为数组存储多个碰撞体
            helpers: [], // 改为数组存储多个辅助器
            visible: false
        };

        // 射线可视化系统
        this.rayVisualization = {
            enabled: true, // 默认启用
            rayHelper: null,
            hitPointHelper: null,
            scene: null,
            maxDistance: 100
        };

        // Armature 包围盒系统
        this.armatureBoundingBox = {
            armature: null,
            boundingBox: null,
            helper: null,
            visible: false,
            scene: null
        };

        // 情绪系统 - 使用专门的情绪状态管理类
        this.emotionState = new EmotionState();

        // 生命值系统
        this.healthSystem = {
            health: 20,
            maxHealth: 20,
            isDown: false,
            lastDamageTime: 0,
            regenerationRate: 0.5,  // 每秒恢复的生命值（倒下状态时恢复更快）
            regenerationDelay: 3000 // 3秒后开始自动恢复（倒下状态时更快开始恢复）
        };

        // 日志控制
        this.lastHealthRecoveryLogTime = 0;
    }

    async init(scene = null) {
        try {
            await this.loadGLTFModel('/hank_pof.glb');
            this.setupModel();
            this.createArmatureCollision();

            if (this.animations.size === 0) {
                this.createBasicAnimations();
            }

            // 如果提供了场景，自动设置射线可视化
            if (scene) {
                this.autoSetupRayVisualization(scene);
            }

            this.isLoaded = true;
            this.isReady = true;

        } catch (error) {
            await this.createPlaceholderModel();
            this.setupModel();
            this.createBasicAnimations();

            // 如果提供了场景，自动设置射线可视化
            if (scene) {
                this.autoSetupRayVisualization(scene);
            }

            this.isLoaded = true;
            this.isReady = true;
        }
    }

    async loadGLTFModel(modelPath) {
        return new Promise((resolve, reject) => {
            this.loader.load(
                modelPath,
                (gltf) => {
                    this.model = gltf.scene;

                    if (gltf.animations && gltf.animations.length > 0) {
                        this.mixer = new THREE.AnimationMixer(this.model);
                        gltf.animations.forEach((clip) => {
                            this.animations.set(clip.name, clip);
                        });
                    }

                    this.initBlinkSystem();
                    this.printShapeKeys(); // 打印所有shape keys
                    resolve(gltf);
                },
                undefined,
                (error) => {
                    reject(error);
                }
            );
        });
    }



    initBlinkSystem() {
        if (!this.model) return;

        this.model.traverse((child) => {
            if (child.isMesh && child.name === 'Cube001_1' && child.morphTargetDictionary) {
                // 初始化眨眼系统
                Object.keys(child.morphTargetDictionary).forEach(keyName => {
                    if (keyName.toLowerCase() === 'blink') {
                        this.blinkMesh = child;
                        this.blinkIndex = child.morphTargetDictionary[keyName];

                        if (child.morphTargetInfluences) {
                            child.morphTargetInfluences[this.blinkIndex] = 0;
                        }

                        this.startBlinkLoop();
                        return;
                    }
                });

                // 初始化嘴型动画系统
                this.initMouthAnimation(child);
            }
        });
    }

    /**
     * 初始化嘴型动画系统
     * @param {THREE.SkinnedMesh} mesh - 包含shape keys的mesh
     */
    initMouthAnimation(mesh) {
        if (!mesh.morphTargetDictionary) return;

        // 查找嘴型相关的shape keys
        this.mouthShapeKeys = {
            mesh: mesh,
            closedIndex: -1,
            openIndex: -1
        };

        Object.keys(mesh.morphTargetDictionary).forEach(keyName => {
            const index = mesh.morphTargetDictionary[keyName];

            if (keyName === 'closed') {
                this.mouthShapeKeys.closedIndex = index;
                console.log(`🗣️ 找到嘴型Shape Key: "closed" (索引: ${index})`);
            } else if (keyName === 'Key 9') {
                this.mouthShapeKeys.openIndex = index;
                console.log(`🗣️ 找到嘴型Shape Key: "Key 9" (索引: ${index})`);
            }
        });

        // 检查是否找到了必要的shape keys
        if (this.mouthShapeKeys.closedIndex !== -1 && this.mouthShapeKeys.openIndex !== -1) {
            console.log('✅ 嘴型动画系统初始化成功');
            // 设置初始状态为闭嘴
            this.setMouthState(0); // 0 = 闭嘴
        } else {
            console.warn('⚠️ 未找到完整的嘴型Shape Keys');
            console.log(`   closed索引: ${this.mouthShapeKeys.closedIndex}`);
            console.log(`   Key 9索引: ${this.mouthShapeKeys.openIndex}`);
        }
    }

    /**
     * 打印模型的所有Shape Keys（变形目标）到控制台
     */
    printShapeKeys() {
        if (!this.model) {
            console.log('❌ 模型未加载，无法打印Shape Keys');
            return;
        }

        console.log('🎭 ===== 模型Shape Keys分析 =====');

        let totalShapeKeys = 0;
        let meshesWithShapeKeys = 0;

        this.model.traverse((child) => {
            if (child.isMesh) {
                const hasShapeKeys = child.morphTargetDictionary && Object.keys(child.morphTargetDictionary).length > 0;

                if (hasShapeKeys) {
                    meshesWithShapeKeys++;
                    console.log(`\n📦 Mesh: "${child.name}"`);
                    console.log(`   ├─ 类型: ${child.type}`);
                    console.log(`   ├─ UUID: ${child.uuid.substring(0, 8)}...`);

                    // 打印Shape Keys字典
                    const shapeKeys = Object.keys(child.morphTargetDictionary);
                    console.log(`   ├─ Shape Keys数量: ${shapeKeys.length}`);

                    shapeKeys.forEach((keyName, index) => {
                        const keyIndex = child.morphTargetDictionary[keyName];
                        const currentValue = child.morphTargetInfluences ? child.morphTargetInfluences[keyIndex] : 'N/A';
                        const isLast = index === shapeKeys.length - 1;
                        const prefix = isLast ? '   └─' : '   ├─';

                        console.log(`${prefix} [${keyIndex}] "${keyName}" (当前值: ${currentValue})`);
                        totalShapeKeys++;
                    });

                    // 打印morphTargetInfluences数组信息
                    if (child.morphTargetInfluences) {
                        console.log(`   ├─ morphTargetInfluences长度: ${child.morphTargetInfluences.length}`);
                        console.log(`   └─ 当前影响值: [${child.morphTargetInfluences.map(v => v.toFixed(3)).join(', ')}]`);
                    } else {
                        console.log(`   └─ morphTargetInfluences: 未定义`);
                    }
                } else {
                    // 简单记录没有Shape Keys的mesh
                    console.log(`📦 Mesh: "${child.name}" - 无Shape Keys`);
                }
            }
        });

        console.log('\n📊 ===== Shape Keys统计 =====');
        console.log(`   总Shape Keys数量: ${totalShapeKeys}`);
        console.log(`   包含Shape Keys的Mesh数量: ${meshesWithShapeKeys}`);

        if (totalShapeKeys === 0) {
            console.log('⚠️ 该模型没有Shape Keys（变形目标）');
        } else {
            console.log('✅ Shape Keys分析完成');
        }

        console.log('═'.repeat(50));
    }

    /**
     * 设置嘴巴状态
     * @param {number} openAmount - 张嘴程度 (0=完全闭合, 1=完全张开)
     */
    setMouthState(openAmount) {
        if (!this.mouthShapeKeys ||
            this.mouthShapeKeys.closedIndex === -1 ||
            this.mouthShapeKeys.openIndex === -1) {
            return;
        }

        // 确保openAmount在0-1范围内
        openAmount = Math.max(0, Math.min(1, openAmount));

        const mesh = this.mouthShapeKeys.mesh;
        if (mesh.morphTargetInfluences) {
            // closed: 1=闭嘴, 0=不闭嘴
            // Key 9: 1=张嘴, 0=不张嘴
            mesh.morphTargetInfluences[this.mouthShapeKeys.closedIndex] = 1 - openAmount;
            mesh.morphTargetInfluences[this.mouthShapeKeys.openIndex] = openAmount;
        }
    }

    /**
     * 开始嘴型动画（说话时调用）
     */
    startMouthAnimation() {
        if (!this.mouthShapeKeys) return;

        this.isMouthAnimating = true;
        this.mouthAnimationStartTime = Date.now();

        // 开始嘴型动画循环
        this.animateMouth();
    }

    /**
     * 停止嘴型动画
     */
    stopMouthAnimation() {
        this.isMouthAnimating = false;
        // 回到闭嘴状态
        this.setMouthState(0);
    }

    /**
     * 嘴型动画循环
     */
    animateMouth() {
        if (!this.isMouthAnimating) return;

        const elapsed = Date.now() - this.mouthAnimationStartTime;

        // 使用正弦波创建自然的说话节奏
        // 频率可以调整，这里设置为每秒约3-4次张合
        const frequency = 0.008; // 调整这个值可以改变说话速度
        const openAmount = (Math.sin(elapsed * frequency) + 1) * 0.5; // 0-1之间

        // 添加一些随机性让动画更自然
        const randomFactor = 0.8 + Math.random() * 0.4; // 0.8-1.2之间
        const finalOpenAmount = Math.min(1, openAmount * randomFactor);

        this.setMouthState(finalOpenAmount);

        // 继续动画
        if (this.isMouthAnimating) {
            requestAnimationFrame(() => this.animateMouth());
        }
    }

    startBlinkLoop() {
        if (!this.blinkSystem.enabled || !this.blinkMesh) return;

        if (this.blinkSystem.blinkAnimationId) {
            cancelAnimationFrame(this.blinkSystem.blinkAnimationId);
        }

        this.blinkSystem.lastBlink = Date.now();

        const blinkLoop = () => {
            if (!this.blinkSystem.enabled) return;

            const now = Date.now();

            if (now - this.blinkSystem.lastBlink > this.blinkSystem.interval && !this.blinkSystem.isBlinking) {
                this.performBlink();
                this.blinkSystem.lastBlink = now;
            }

            this.blinkSystem.blinkAnimationId = requestAnimationFrame(blinkLoop);
        };

        blinkLoop();
    }

    performBlink() {
        if (!this.blinkMesh || this.blinkSystem.isBlinking) return;

        this.blinkSystem.isBlinking = true;

        const startTime = Date.now();
        const duration = this.blinkSystem.duration;

        const animateBlink = () => {
            const elapsed = Date.now() - startTime;
            const progress = elapsed / duration;

            if (progress <= 1) {
                const blinkValue = Math.sin(progress * Math.PI);

                if (this.blinkMesh.morphTargetInfluences) {
                    this.blinkMesh.morphTargetInfluences[this.blinkIndex] = blinkValue;
                }

                requestAnimationFrame(animateBlink);
            } else {
                if (this.blinkMesh.morphTargetInfluences) {
                    this.blinkMesh.morphTargetInfluences[this.blinkIndex] = 0;
                }
                this.blinkSystem.isBlinking = false;
            }
        };

        animateBlink();
    }

    triggerBlink() {
        if (!this.blinkSystem.isBlinking) {
            this.performBlink();
        }
    }

    setBlinkInterval(intervalMs) {
        this.blinkSystem.interval = intervalMs;
    }

    setBlinkEnabled(enabled) {
        this.blinkSystem.enabled = enabled;

        if (enabled && this.blinkMesh && !this.blinkSystem.blinkAnimationId) {
            this.startBlinkLoop();
        } else if (!enabled && this.blinkSystem.blinkAnimationId) {
            cancelAnimationFrame(this.blinkSystem.blinkAnimationId);
            this.blinkSystem.blinkAnimationId = null;

            if (this.blinkMesh && this.blinkMesh.morphTargetInfluences) {
                this.blinkMesh.morphTargetInfluences[this.blinkIndex] = 0;
            }
        }
    }

    getBlinkStatus() {
        return {
            enabled: this.blinkSystem.enabled,
            interval: this.blinkSystem.interval,
            duration: this.blinkSystem.duration,
            isBlinking: this.blinkSystem.isBlinking,
            hasBlinkShapeKey: !!this.blinkMesh,
            blinkMeshName: this.blinkMesh ? this.blinkMesh.name : null,
            blinkIndex: this.blinkIndex
        };
    }



    setupModel() {
        if (!this.model) return;

        const box = new THREE.Box3().setFromObject(this.model);
        const size = box.getSize(new THREE.Vector3());
        const targetHeight = 2.5;
        const scale = targetHeight / size.y;

        this.model.scale.set(scale, scale, scale);

        const scaledBox = new THREE.Box3().setFromObject(this.model);
        const bottomY = scaledBox.min.y;
        this.model.position.set(0, -bottomY, 0);

        // 存储原始设置信息，用于后续调整
        this.modelSetup = {
            originalSize: size.clone(),
            scale: scale,
            targetHeight: targetHeight,
            bottomOffset: -bottomY
        };

        // 设置碰撞检测标记
        const mesh = this.model.getObjectByName('Cube001_Remeshed');
        if (mesh) {
            mesh.userData.isCollisionEnabled = true;
            mesh.userData.collisionType = 'body';
            mesh.userData.canBeClicked = true;
        }

        this.model.traverse((child) => {
            if (child.isMesh) {
                child.castShadow = true;
                child.receiveShadow = true;

                if (child.material) {
                    if (Array.isArray(child.material)) {
                        child.material.forEach(mat => {
                            if (mat.map) mat.map.flipY = false;
                        });
                    } else {
                        if (child.material.map) child.material.map.flipY = false;
                    }
                }
            }
        });

        // 初始化 Armature 包围盒系统
        this.initArmatureBoundingBox();
    }

    /**
     * 创建 Armature 碰撞体系统 - 基于 Armature 的简化碰撞检测
     */
    createArmatureCollision() {
        if (!this.model) return;

        // 1. 获取 Armature 对象
        const armature = this.model.getObjectByName('Armature');



        if (!armature) {
            console.warn('⚠️ 未找到 Armature 对象，无法创建碰撞体');
            return;
        }

        // 2. 基于整个 Armature 生成世界坐标包围盒
        const armatureBox = new THREE.Box3().setFromObject(armature);
        const size = armatureBox.getSize(new THREE.Vector3());
        const center = armatureBox.getCenter(new THREE.Vector3());

        // 3. 创建两个拼接的碰撞体网格
        const boxWidth = size.x * 1.2;
        const boxHeight = size.y * 3.0;
        const boxDepth = size.z * 1.2;

        // 第一个碰撞盒（下方）
        const geometry1 = new THREE.BoxGeometry(boxWidth, boxHeight, boxDepth);
        const material1 = new THREE.MeshBasicMaterial({
            color: 0xff0000,
            transparent: true,
            opacity: 0.3,
            visible: this.armatureCollision.visible,
            wireframe: false
        });

        const collisionMesh1 = new THREE.Mesh(geometry1, material1);
        collisionMesh1.position.copy(center);
        collisionMesh1.name = 'armature_collision_1';
        collisionMesh1.userData = {
            isArmatureCollision: true,
            armature: armature,
            boxIndex: 0
        };

        // 第二个碰撞盒（上方，与第一个拼接）
        const geometry2 = new THREE.BoxGeometry(boxWidth, boxHeight, boxDepth);
        const material2 = new THREE.MeshBasicMaterial({
            color: 0x00ff00, // 绿色以便区分
            transparent: true,
            opacity: 0.3,
            visible: this.armatureCollision.visible,
            wireframe: false
        });

        const collisionMesh2 = new THREE.Mesh(geometry2, material2);
        // 计算第二个盒子的位置：第一个盒子顶部 + 第二个盒子高度的一半
        const secondBoxY = center.y + boxHeight; // 第一个盒子的顶部就是第二个盒子的底部
        collisionMesh2.position.set(center.x, secondBoxY, center.z);
        collisionMesh2.name = 'armature_collision_2';
        collisionMesh2.userData = {
            isArmatureCollision: true,
            armature: armature,
            boxIndex: 1
        };

        // 4. 创建两个包围盒辅助器
        // 第一个辅助器（下方）
        const expandedBox1 = new THREE.Box3();
        expandedBox1.setFromCenterAndSize(
            center,
            new THREE.Vector3(boxWidth, boxHeight, boxDepth)
        );
        const helper1 = new THREE.Box3Helper(expandedBox1, 0xff0000);

        // 第二个辅助器（上方）
        const expandedBox2 = new THREE.Box3();
        expandedBox2.setFromCenterAndSize(
            new THREE.Vector3(center.x, secondBoxY, center.z),
            new THREE.Vector3(boxWidth, boxHeight, boxDepth)
        );
        const helper2 = new THREE.Box3Helper(expandedBox2, 0x00ff00);

        // 5. 将所有碰撞体添加到场景
        this.model.add(collisionMesh1);
        this.model.add(collisionMesh2);
        this.model.add(helper1);
        this.model.add(helper2);

        // 6. 存储引用
        this.armatureCollision.armature = armature;
        this.armatureCollision.boundingBox = armatureBox;
        this.armatureCollision.collisionMeshes = [collisionMesh1, collisionMesh2];
        this.armatureCollision.helpers = [helper1, helper2];

        console.log('✅ Armature 碰撞体创建完成');
    }

    /**
     * 初始化 Armature 包围盒系统
     */
    initArmatureBoundingBox() {
        if (!this.model) return;

        const armature = this.model.getObjectByName('Armature');
        if (armature) {
            this.armatureBoundingBox.armature = armature;
            this.armatureBoundingBox.scene = this.rayVisualization.scene;
            console.log('✅ Armature 包围盒系统初始化完成');
        } else {
            console.warn('⚠️ 未找到 Armature 对象');
        }
    }

    createBasicAnimations() {
        this.mixer = new THREE.AnimationMixer(this.model);
        this.createIdleAnimation();
    }

    createIdleAnimation() {
        if (!this.bodyParts) return;

        const times = [0, 1, 2];
        const values = [0, 0.1, 0];

        const positionKF = new THREE.VectorKeyframeTrack(
            '.position[y]',
            times,
            values
        );

        const clip = new THREE.AnimationClip('idle', 2, [positionKF]);
        this.animations.set('stand', clip);
    }

    async createPlaceholderModel() {
        const group = new THREE.Group();

        const bodyGeometry = new THREE.CapsuleGeometry(0.8, 1.5, 4, 8);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 1.2;
        body.castShadow = true;
        group.add(body);

        const headGeometry = new THREE.SphereGeometry(0.6, 16, 16);
        const headMaterial = new THREE.MeshLambertMaterial({ color: 0xD2691E });
        const head = new THREE.Mesh(headGeometry, headMaterial);
        head.position.y = 2.2;
        head.castShadow = true;
        group.add(head);

        this.model = group;
        this.bodyParts = { body: body, head: head };
    }

    getMixer() {
        return this.mixer;
    }

    update(deltaTime) {
        if (this.mixer) {
            this.mixer.update(deltaTime);
        }
    }

    dispose() {
        if (this.mixer) {
            this.mixer.stopAllAction();
        }

        if (this.model) {
            this.model.traverse((child) => {
                if (child.geometry) child.geometry.dispose();
                if (child.material) {
                    if (Array.isArray(child.material)) {
                        child.material.forEach(material => material.dispose());
                    } else {
                        child.material.dispose();
                    }
                }
            });
        }

        // 清理射线可视化
        this.clearRayVisualization();
        this.clearHitPointVisualization();
    }

    /**
     * 切换 Armature 碰撞体可视化
     */
    toggleArmatureCollision(visible = null) {
        this.armatureCollision.visible = visible !== null ? visible : !this.armatureCollision.visible;

        // 切换所有碰撞体网格可见性
        this.armatureCollision.collisionMeshes.forEach((mesh) => {
            if (mesh && mesh.material) {
                mesh.material.visible = this.armatureCollision.visible;
            }
        });

        // 切换所有包围盒辅助器可见性
        this.armatureCollision.helpers.forEach((helper) => {
            if (helper) {
                helper.visible = this.armatureCollision.visible;
            }
        });

        console.log(`📦 Armature 碰撞体可视化: ${this.armatureCollision.visible ? '开启' : '关闭'} (${this.armatureCollision.collisionMeshes.length}个碰撞盒)`);
        return this.armatureCollision.visible;
    }

    /**
     * 获取 Cube001_Remeshed mesh 对象
     * @returns {THREE.Mesh|null} mesh 对象或 null
     */
    getCube001RemeshedMesh() {
        if (!this.model) {
            return null;
        }

        let targetMesh = null;
        this.model.traverse((child) => {
            if (child.isMesh && child.name === 'Cube001_Remeshed') {
                targetMesh = child;
            }
        });

        return targetMesh;
    }

    /**
     * 检测射线与 Cube001_Remeshed mesh 的碰撞
     * @param {THREE.Raycaster} raycaster - 射线投射器
     * @returns {Array} 碰撞结果数组
     */
    checkRayCollision(raycaster) {
        const mesh = this.getCube001RemeshedMesh();
        if (!mesh || !mesh.userData.isCollisionEnabled) {
            return [];
        }

        const intersects = [];
        raycaster.intersectObject(mesh, false, intersects);

        return intersects.map(intersect => ({
            ...intersect,
            collisionType: 'body',
            meshName: 'Cube001_Remeshed',
            isMainBody: true
        }));
    }

    /**
     * 检测点击碰撞（鼠标/触摸）
     * @param {number} x - 屏幕 X 坐标
     * @param {number} y - 屏幕 Y 坐标
     * @param {THREE.Camera} camera - 相机对象
     * @param {HTMLElement} domElement - DOM 元素（用于获取尺寸）
     * @returns {Array} 碰撞结果数组
     */
    checkClickCollision(x, y, camera, domElement) {
        // 将屏幕坐标转换为标准化设备坐标
        const rect = domElement.getBoundingClientRect();
        const mouse = new THREE.Vector2();
        mouse.x = ((x - rect.left) / rect.width) * 2 - 1;
        mouse.y = -((y - rect.top) / rect.height) * 2 + 1;

        // 创建射线投射器
        const raycaster = new THREE.Raycaster();
        raycaster.setFromCamera(mouse, camera);

        return this.checkRayCollision(raycaster);
    }

    /**
     * 检测两个对象之间的碰撞（边界盒检测）
     * @param {THREE.Object3D} otherObject - 另一个对象
     * @returns {boolean} 是否发生碰撞
     */
    checkObjectCollision(otherObject) {
        const mesh = this.getCube001RemeshedMesh();
        if (!mesh || !mesh.userData.isCollisionEnabled) {
            return false;
        }

        // 获取两个对象的边界盒
        const meshBox = mesh.userData.boundingBox || new THREE.Box3().setFromObject(mesh);
        const otherBox = new THREE.Box3().setFromObject(otherObject);

        // 检测边界盒是否相交
        return meshBox.intersectsBox(otherBox);
    }

    /**
     * 切换 Cube001_Remeshed 的碰撞检测可视化
     * @param {boolean} visible - 是否显示碰撞边界盒
     */
    toggleMeshCollisionVisualization(visible = null) {
        const mesh = this.getCube001RemeshedMesh();
        if (!mesh) return false;

        const shouldShow = visible !== null ? visible : !mesh.userData.showCollisionBox;
        mesh.userData.showCollisionBox = shouldShow;

        // 如果还没有边界盒辅助器，创建一个
        if (!mesh.userData.boundingBoxHelper && shouldShow) {
            const boundingBox = mesh.userData.boundingBox || new THREE.Box3().setFromObject(mesh);
            mesh.userData.boundingBoxHelper = new THREE.Box3Helper(boundingBox, 0x00ff00);
            mesh.add(mesh.userData.boundingBoxHelper);
        }

        // 切换可见性
        if (mesh.userData.boundingBoxHelper) {
            mesh.userData.boundingBoxHelper.visible = shouldShow;
        }

        console.log(`📦 Cube001_Remeshed 碰撞可视化: ${shouldShow ? '开启' : '关闭'}`);
        return shouldShow;
    }





    getCollisionBoxes() {
        return this.armatureCollision.collisionMeshes || [];
    }

    /**
     * 获取 Armature 碰撞体数组
     */
    getArmatureCollision() {
        return this.armatureCollision.collisionMeshes;
    }

    /**
     * 设置点击检测系统
     * 为指定的 mesh 添加包围盒辅助器和点击标记
     * @param {string} meshName - 要设置的 mesh 名称，默认为 'Cube001_Remeshed'
     * @param {boolean} showHelper - 是否显示包围盒辅助器
     */
    setupClickDetection(meshName = 'Cube001_Remeshed', showHelper = true) {
        if (!this.model) {
            return false;
        }

        // 1. 获取指定的 Mesh
        const bodyMesh = this.model.getObjectByName(meshName);
        if (bodyMesh) {
            // 2. 在 bodyMesh 本地空间生成包围盒辅助器（可视化碰撞区域）
            const bbox = new THREE.Box3().setFromObject(bodyMesh);
            const helper = new THREE.Box3Helper(bbox, 0x00ff00);

            // 如果已经有辅助器，先移除
            if (bodyMesh.userData.clickHelper) {
                bodyMesh.remove(bodyMesh.userData.clickHelper);
            }

            bodyMesh.add(helper);
            bodyMesh.userData.clickHelper = helper;
            helper.visible = showHelper;

            // 3. 标记为可点击
            bodyMesh.userData.canBeClicked = true;
            bodyMesh.userData.clickDetectionEnabled = true;

            return bodyMesh;
        } else {
            return null;
        }
    }

    /**
     * 初始化全局点击事件监听器
     * 这个方法应该在场景初始化时调用一次
     * @param {THREE.WebGLRenderer} renderer - 渲染器
     * @param {THREE.Camera} camera - 相机
     * @param {Function} onClickCallback - 点击回调函数
     */
    initGlobalClickListener(renderer, camera, onClickCallback = null) {
        if (!renderer || !camera) {
            return false;
        }

        // 存储引用以便后续使用
        this.clickDetection = {
            renderer: renderer,
            camera: camera,
            raycaster: new THREE.Raycaster(),
            mouse: new THREE.Vector2(),
            callback: onClickCallback,
            isInitialized: true
        };

        // 添加点击事件监听器
        const clickHandler = (event) => {
            this.handleClick(event);
        };

        renderer.domElement.addEventListener('click', clickHandler);

        // 存储事件处理器引用以便后续移除
        this.clickDetection.clickHandler = clickHandler;

        return true;
    }

    /**
     * 处理点击事件
     * @param {MouseEvent} event - 鼠标事件
     */
    handleClick(event) {
        if (!this.clickDetection || !this.clickDetection.isInitialized) {
            return;
        }

        const { renderer, camera, raycaster, mouse } = this.clickDetection;

        // 转换屏幕坐标到 NDC
        const rect = renderer.domElement.getBoundingClientRect();
        mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

        raycaster.setFromCamera(mouse, camera);

        // 可视化射线
        this.visualizeRay(raycaster);

        // 检测所有可点击的 mesh
        const clickableMeshes = [];
        if (this.model) {
            this.model.traverse((child) => {
                if (child.isMesh && child.userData.canBeClicked) {
                    clickableMeshes.push(child);
                }
            });
        }

        if (clickableMeshes.length === 0) {
            return;
        }

        // 进行射线检测
        const hits = raycaster.intersectObjects(clickableMeshes, true);

        if (hits.length > 0) {
            const hitObject = hits[0].object;

            // 可视化命中点
            this.visualizeHitPoint(hits[0].point);

            // 调用回调函数
            if (this.clickDetection.callback) {
                this.clickDetection.callback({
                    mesh: hitObject,
                    hit: hits[0],
                    allHits: hits
                });
            }

            // 触发默认交互逻辑
            this.onMeshClicked(hitObject, hits[0]);
        } else {
            // 没有命中时清除命中点可视化
            this.clearHitPointVisualization();
        }
    }

    /**
     * 默认的 mesh 点击处理逻辑
     * 可以被重写以实现自定义行为
     * @param {THREE.Mesh} mesh - 被点击的 mesh
     * @param {Object} hitInfo - 射线检测结果
     */
    onMeshClicked(mesh) {
        // 可以在这里添加默认的点击处理逻辑
    }

    /**
     * 初始化射线可视化系统
     * @param {THREE.Scene} scene - 场景对象
     */
    initRayVisualization(scene) {
        this.rayVisualization.scene = scene;
        this.rayVisualization.enabled = true;
    }

    /**
     * 自动设置射线可视化（在模型加载后自动调用）
     * @param {THREE.Scene} scene - 场景对象
     */
    autoSetupRayVisualization(scene) {
        if (scene) {
            this.initRayVisualization(scene);
        }
    }

    /**
     * 可视化射线
     * @param {THREE.Raycaster} raycaster - 射线投射器
     */
    visualizeRay(raycaster) {
        if (!this.rayVisualization.enabled || !this.rayVisualization.scene) {
            return;
        }

        // 清除之前的射线可视化
        this.clearRayVisualization();

        // 创建射线几何体
        const origin = raycaster.ray.origin;
        const direction = raycaster.ray.direction;
        const distance = this.rayVisualization.maxDistance;

        // 计算射线终点
        const endPoint = origin.clone().add(direction.clone().multiplyScalar(distance));

        // 创建线条几何体
        const geometry = new THREE.BufferGeometry().setFromPoints([origin, endPoint]);
        const material = new THREE.LineBasicMaterial({
            color: 0xff0000,
            linewidth: 2,
            transparent: true,
            opacity: 0.8
        });

        // 创建射线线条
        this.rayVisualization.rayHelper = new THREE.Line(geometry, material);
        this.rayVisualization.scene.add(this.rayVisualization.rayHelper);

        // 设置自动清除定时器（2秒后清除）
        setTimeout(() => {
            this.clearRayVisualization();
        }, 2000);
    }

    /**
     * 可视化命中点
     * @param {THREE.Vector3} hitPoint - 命中点坐标
     */
    visualizeHitPoint(hitPoint) {
        if (!this.rayVisualization.enabled || !this.rayVisualization.scene) {
            return;
        }

        // 清除之前的命中点可视化
        this.clearHitPointVisualization();

        // 创建命中点标记（小球）
        const geometry = new THREE.SphereGeometry(0.05, 8, 8);
        const material = new THREE.MeshBasicMaterial({
            color: 0x00ff00,
            transparent: true,
            opacity: 0.9
        });

        this.rayVisualization.hitPointHelper = new THREE.Mesh(geometry, material);
        this.rayVisualization.hitPointHelper.position.copy(hitPoint);
        this.rayVisualization.scene.add(this.rayVisualization.hitPointHelper);

        // 设置自动清除定时器（3秒后清除）
        setTimeout(() => {
            this.clearHitPointVisualization();
        }, 3000);
    }

    /**
     * 清除射线可视化
     */
    clearRayVisualization() {
        if (this.rayVisualization.rayHelper && this.rayVisualization.scene) {
            this.rayVisualization.scene.remove(this.rayVisualization.rayHelper);
            this.rayVisualization.rayHelper.geometry.dispose();
            this.rayVisualization.rayHelper.material.dispose();
            this.rayVisualization.rayHelper = null;
        }
    }

    /**
     * 清除命中点可视化
     */
    clearHitPointVisualization() {
        if (this.rayVisualization.hitPointHelper && this.rayVisualization.scene) {
            this.rayVisualization.scene.remove(this.rayVisualization.hitPointHelper);
            this.rayVisualization.hitPointHelper.geometry.dispose();
            this.rayVisualization.hitPointHelper.material.dispose();
            this.rayVisualization.hitPointHelper = null;
        }
    }

    /**
     * 切换射线可视化开关
     * @param {boolean} enabled - 是否启用射线可视化
     */
    setRayVisualizationEnabled(enabled) {
        this.rayVisualization.enabled = enabled;

        if (!enabled) {
            this.clearRayVisualization();
            this.clearHitPointVisualization();
        }
    }

    /**
     * 设置射线可视化的最大距离
     * @param {number} distance - 最大距离
     */
    setRayVisualizationDistance(distance) {
        this.rayVisualization.maxDistance = distance;
    }

    /**
     * 获取射线可视化状态
     * @returns {Object} 射线可视化状态信息
     */
    getRayVisualizationStatus() {
        return {
            enabled: this.rayVisualization.enabled,
            maxDistance: this.rayVisualization.maxDistance,
            hasScene: !!this.rayVisualization.scene,
            hasActiveRay: !!this.rayVisualization.rayHelper,
            hasActiveHitPoint: !!this.rayVisualization.hitPointHelper
        };
    }

    /**
     * 切换点击检测辅助器的可见性
     * @param {boolean} visible - 是否显示
     */
    toggleClickDetectionHelper(visible = null) {
        if (!this.model) return false;

        let helperCount = 0;
        this.model.traverse((child) => {
            if (child.isMesh && child.userData.clickHelper) {
                const shouldShow = visible !== null ? visible : !child.userData.clickHelper.visible;
                child.userData.clickHelper.visible = shouldShow;
                helperCount++;
            }
        });

        return helperCount > 0;
    }

    /**
     * 移除点击检测系统
     */
    removeClickDetection() {
        // 移除事件监听器
        if (this.clickDetection && this.clickDetection.clickHandler) {
            this.clickDetection.renderer.domElement.removeEventListener('click', this.clickDetection.clickHandler);
        }

        // 移除辅助器
        if (this.model) {
            this.model.traverse((child) => {
                if (child.isMesh && child.userData.clickHelper) {
                    child.remove(child.userData.clickHelper);
                    delete child.userData.clickHelper;
                    delete child.userData.canBeClicked;
                    delete child.userData.clickDetectionEnabled;
                }
            });
        }

        // 清理引用
        this.clickDetection = null;
    }

    /**
     * 调整模型高度到指定的地面高度
     * @param {number} groundLevel - 地面高度，默认为0
     * @param {number} offset - 额外的偏移量，默认为0.05（稍微抬高避免Z-fighting）
     */
    adjustToGroundLevel(groundLevel = 0, offset = 0.05) {
        if (!this.model) {
            return false;
        }

        // 获取当前模型的边界盒
        const box = new THREE.Box3().setFromObject(this.model);
        const currentBottom = box.min.y;
        const targetY = groundLevel + offset;
        const adjustment = targetY - currentBottom;

        // 调整模型位置
        this.model.position.y += adjustment;

        return true;
    }

    /**
     * 获取模型的高度信息
     * @returns {Object} 包含模型高度信息的对象
     */
    getModelHeightInfo() {
        if (!this.model) {
            console.warn('❌ 模型未加载');
            return null;
        }

        const box = new THREE.Box3().setFromObject(this.model);
        const size = box.getSize(new THREE.Vector3());
        const center = box.getCenter(new THREE.Vector3());

        return {
            boundingBox: {
                min: box.min.clone(),
                max: box.max.clone(),
                size: size.clone(),
                center: center.clone()
            },
            position: this.model.position.clone(),
            scale: this.model.scale.clone(),
            height: size.y,
            bottomY: box.min.y,
            topY: box.max.y,
            setup: this.modelSetup ? { ...this.modelSetup } : null
        };
    }

    /**
     * 设置模型的绝对Y位置
     * @param {number} y - Y坐标
     */
    setModelY(y) {
        if (!this.model) {
            return false;
        }

        this.model.position.y = y;
        return true;
    }

    /**
     * 相对调整模型Y位置
     * @param {number} deltaY - Y轴偏移量
     */
    adjustModelY(deltaY) {
        if (!this.model) {
            return false;
        }

        this.model.position.y += deltaY;
        return true;
    }

    /**
     * 重置模型到初始设置状态
     */
    resetModelPosition() {
        if (!this.model || !this.modelSetup) {
            return false;
        }

        // 重新应用初始设置
        const { scale, bottomOffset } = this.modelSetup;

        this.model.scale.set(scale, scale, scale);
        this.model.position.set(0, bottomOffset, 0);

        return true;
    }



    /**
     * 获取模型高度相关信息
     */
    getModelHeightInfo() {
        if (!this.model) {
            return { error: '模型未加载' };
        }

        const armature = this.model.getObjectByName('Armature');
        const modelBox = new THREE.Box3().setFromObject(this.model);

        const info = {
            model: {
                position: {
                    x: this.model.position.x,
                    y: this.model.position.y,
                    z: this.model.position.z
                },
                boundingBox: {
                    min: { x: modelBox.min.x, y: modelBox.min.y, z: modelBox.min.z },
                    max: { x: modelBox.max.x, y: modelBox.max.y, z: modelBox.max.z },
                    size: {
                        x: modelBox.max.x - modelBox.min.x,
                        y: modelBox.max.y - modelBox.min.y,
                        z: modelBox.max.z - modelBox.min.z
                    }
                }
            },
            armature: armature ? {
                position: {
                    x: armature.position.x,
                    y: armature.position.y,
                    z: armature.position.z
                }
            } : null,
            setup: this.modelSetup || null
        };

        return info;
    }

    // ==================== 情绪系统方法 ====================

    /**
     * 设置情绪状态
     * @param {string} emotion - 情绪类型 (happiness, energy, alertness, friendliness, anger, fear, snark, playfulness)
     * @param {number} value - 情绪值 (0-1)
     */
    setEmotionalState(emotion, value) {
        return this.emotionState.setEmotion(emotion, value);
    }

    /**
     * 获取当前情绪状态
     * @returns {Object} 情绪状态副本
     */
    getEmotionalState() {
        return this.emotionState.getEmotions();
    }

    /**
     * 获取主导情绪
     * @returns {string} 主导情绪名称
     */
    getDominantEmotion() {
        return this.emotionState.getDominantEmotion();
    }

    /**
     * 更新情绪状态（基于行为类型）
     * @param {string} behaviorType - 行为类型
     */
    updateEmotionalState(behaviorType) {
        this.emotionState.updateFromEvent(behaviorType);
    }

    // 情绪相互影响和衰减现在由 EmotionState 类管理

    // 情绪权重修正现在由 BehaviorSystem 类管理

    // ==================== 生命值系统方法 ====================

    /**
     * 获取当前生命值
     * @returns {number} 当前生命值
     */
    getHealth() {
        return this.healthSystem.health;
    }

    /**
     * 获取最大生命值
     * @returns {number} 最大生命值
     */
    getMaxHealth() {
        return this.healthSystem.maxHealth;
    }

    /**
     * 设置生命值
     * @param {number} health - 新的生命值
     */
    setHealth(health) {
        const oldHealth = this.healthSystem.health;
        this.healthSystem.health = Math.max(0, Math.min(this.healthSystem.maxHealth, health));

        if (this.healthSystem.health < oldHealth) {
            this.healthSystem.lastDamageTime = Date.now();
            console.log(`💔 生命值减少: ${oldHealth} → ${this.healthSystem.health}`);
        }
        // 移除生命值恢复的频繁日志输出

        // 检查是否倒下
        if (this.healthSystem.health <= 0 && !this.healthSystem.isDown) {
            this.setDown(true);
        } else if (this.healthSystem.health > 0 && this.healthSystem.isDown) {
            this.setDown(false);
        }

        return this.healthSystem.health;
    }

    /**
     * 造成伤害
     * @param {number} damage - 伤害值
     * @returns {number} 实际造成的伤害
     */
    takeDamage(damage) {
        const oldHealth = this.healthSystem.health;
        this.setHealth(this.healthSystem.health - damage);
        const actualDamage = oldHealth - this.healthSystem.health;

        // 影响情绪状态
        if (actualDamage > 0) {
            this.emotionState.updateFromEvent('damage', { damage: actualDamage });
        }

        return actualDamage;
    }

    /**
     * 恢复生命值
     * @param {number} amount - 恢复量
     * @returns {number} 实际恢复的生命值
     */
    heal(amount) {
        const oldHealth = this.healthSystem.health;
        this.setHealth(this.healthSystem.health + amount);
        const actualHeal = this.healthSystem.health - oldHealth;

        // 影响情绪状态
        if (actualHeal > 0) {
            this.emotionState.updateFromEvent('heal', { heal: actualHeal });
        }

        return actualHeal;
    }

    /**
     * 设置倒下状态
     * @param {boolean} isDown - 是否倒下
     */
    setDown(isDown) {
        if (this.healthSystem.isDown !== isDown) {
            this.healthSystem.isDown = isDown;
            console.log(`${isDown ? '💀 功夫狗倒下了' : '✨ 功夫狗站起来了'}`);

            // 影响情绪状态
            if (isDown) {
                this.emotionState.updateFromEvent('down');
            } else {
                // 复活时重置情绪到默认值
                this.emotionState.reset();
                console.log('🎭 复活时情绪重置为默认值');
            }
        }
    }

    /**
     * 检查是否倒下
     * @returns {boolean} 是否倒下
     */
    isDownState() {
        return this.healthSystem.isDown;
    }

    /**
     * 完全恢复生命值（手动复活）
     */
    fullHeal() {
        this.setHealth(this.healthSystem.maxHealth);
        this.setDown(false);
        console.log('✨ 手动完全恢复！');
    }

    /**
     * 更新生命值系统（只在倒下状态时自动恢复）
     * @param {number} deltaTime - 时间间隔（毫秒）
     */
    updateHealthSystem(deltaTime) {
        // 只在倒下状态时自动恢复生命值
        if (this.healthSystem.health < this.healthSystem.maxHealth &&
            this.healthSystem.isDown &&
            Date.now() - this.healthSystem.lastDamageTime > this.healthSystem.regenerationDelay) {

            const regenAmount = this.healthSystem.regenerationRate * (deltaTime / 1000);
            this.heal(regenAmount);

            // 减少日志频率，避免刷屏
            if (!this.lastHealthRecoveryLogTime || Date.now() - this.lastHealthRecoveryLogTime > 2000) {
                console.log(`💚 倒下状态中，生命值缓慢恢复: ${this.healthSystem.health.toFixed(1)}/${this.healthSystem.maxHealth}`);
                this.lastHealthRecoveryLogTime = Date.now();
            }

            // 当生命值恢复满时自动复活
            if (this.healthSystem.health >= this.healthSystem.maxHealth) {
                console.log('✨ 生命值已满，自动复活！');
                this.setDown(false);
            }
        }
    }

    /**
     * 获取生命值系统状态
     * @returns {Object} 生命值系统状态
     */
    getHealthStatus() {
        return {
            health: this.healthSystem.health,
            maxHealth: this.healthSystem.maxHealth,
            isDown: this.healthSystem.isDown,
            healthPercentage: (this.healthSystem.health / this.healthSystem.maxHealth) * 100,
            canRegenerate: Date.now() - this.healthSystem.lastDamageTime > this.healthSystem.regenerationDelay
        };
    }

    // ==================== 系统更新方法 ====================

    /**
     * 更新角色系统（每帧调用）
     * @param {number} deltaTime - 时间间隔（毫秒）
     */
    update(deltaTime) {
        // 更新动画混合器
        if (this.mixer) {
            this.mixer.update(deltaTime / 1000);
        }

        // 眨眼系统通过 startBlinkLoop() 自动运行，不需要手动更新

        // 只在倒下状态时进行情绪恢复到默认值
        if (this.healthSystem.isDown) {
            this.emotionState.applyDownStateRecovery();
        }

        // 更新生命值系统
        this.updateHealthSystem(deltaTime);
    }

    /**
     * 销毁功夫狗对象
     */
    dispose() {
        if (this.mixer) {
            this.mixer.stopAllAction();
        }

        if (this.model && this.model.parent) {
            this.model.parent.remove(this.model);
        }

        this.animations.clear();
        this.isLoaded = false;
        this.isReady = false;

        console.log('🗑️ 功夫狗对象已销毁');
    }

}